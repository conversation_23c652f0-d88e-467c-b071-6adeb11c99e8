import { But<PERSON>, <PERSON>lt<PERSON> } from 'antd';
import { Fragment, useEffect, useRef, useState } from 'react';
import useSize from '../../../hooks/useSize';
import TruncateString from '../TruncateString';
import { ChevronDown, LoaderCircle, XIcon, Info } from 'lucide-react';

const UserOptions = ({ option, value, setSearch, handleDataChange }) => {
  let isAssigned = value?.find((item) => item.value === option.value) || false;
  return (
    <>
      <li className="flex items-center gap-2 cursor-pointer hover:bg-slate-100 p-2 bg-white border rounded-lg">
        <p className="py-2 px-4 rounded-full bg-slate-300 text-slate-500">
          {option?.label?.charAt(0)?.toUpperCase()}
        </p>
        <p className="w-[70%] truncate">{option?.label}</p>
        <Button
          className="text-sm"
          type="primary"
          variant={`${isAssigned ? 'outlined' : 'primary'}`}
          onClick={() => {
            setSearch('');
            const exists = value?.find((item) => item.value === option.value);
            handleDataChange(
              (() => {
                if (exists) {
                  return value.filter((item) => item.value !== option.value);
                } else {
                  return [...value, option];
                }
              })(),
              exists ? 'remove' : 'single',
              option
            );
          }}
        >
          {isAssigned ? 'Unassign' : 'Assign'}
        </Button>
      </li>
    </>
  );
};

const LiComp = ({
  option,
  setSearch,
  handleDataChange,
  closeOnSelect,
  value,
  setExpand,
  trimmedOptionLength,
  isPreferred = false,
  isSingle = false,
  getOptionTooltip,
}) => {
  const isLongLabel = option.label.length > trimmedOptionLength;
  return (
    <>
      <li
        className={`w-full py-2 px-2 hover:bg-blue-light rounded-[4px] justify-between cursor-pointer flex items-center ${
          option?.isDisabled
            ? 'pointer-events-none line-through text-gray-500'
            : ''
        }`}
        onClick={() => {
          const exists = value?.find((item) => item.value === option.value);
          setSearch('');
          handleDataChange(
            !isSingle
              ? (() => {
                  if (exists) {
                    return value.filter((item) => item.value !== option.value);
                  } else {
                    return [...value, option];
                  }
                })()
              : [option],
            exists ? 'remove' : 'single',
            option
          );
          if (closeOnSelect) {
            setExpand(false);
          }
        }}
        title={isLongLabel ? option.label : ''}
      >
        <div className="flex items-center gap-x-2">
          {!isSingle && (
            <input
              type="checkbox"
              className="pointer-events-none"
              checked={
                value?.find((item) => item.value === option.value) || false
              }
              disabled={option?.isDisabled}
              readOnly
            />
          )}
          {getOptionTooltip && getOptionTooltip(option) ? (
            <Tooltip title={getOptionTooltip(option)}>
              <Info
                color="blue"
                className="ml-1 h-4 w-4 text-gray-400 cursor-pointer"
              />
            </Tooltip>
          ) : null}
          <span className={`truncate`}>
            {isLongLabel ? `${option.label.slice(0, 15)}...` : option.label}
          </span>
        </div>
        {isPreferred && (
          <span className="text-white text-[8px] w-fit px-[5px] py-[1px] rounded-[8px] bg-purple-500">
            PREFERRED
          </span>
        )}
      </li>
    </>
  );
};

const MultiSelect = ({
  value: newVal = [],
  onChange,
  AddOption,
  handleAddFunction,
  options = [],
  className = '',
  innerClassname = '',
  placeholder = 'Select',
  name = '',
  closeOnSelect = false,
  disabled = false,
  enableSelectAll = true,
  doSearch = true,
  onSearch,
  userOptions = false,
  expanded = false,
  trimmedOptionLength = 15,
  preferredOptions = [],
  isSingle = false,
  isLoading = false,
  getOptionTooltip,
}) => {
  const [search, setSearch] = useState('');
  const [expand, setExpand] = useState(expanded ? true : false);
  const [isMouseOver, setIsMouseOver] = useState(false);
  const [visibleTiles, setVisibleTiles] = useState(0);
  const [styles, setStyles] = useState({});

  const isObject = newVal?.[0]?.label || newVal?.[0]?.value;

  const isGroup = !!options?.find((i) => i.options);

  const allGroupOptions = [];

  if (isGroup)
    options.forEach((opt) =>
      opt.options?.forEach((subOpt) => allGroupOptions.push(subOpt))
    );

  const value = isObject
    ? newVal
    : options?.filter((opt) => newVal?.includes(opt?.value));

  const ref = useRef(null);
  const dropdownRef = useRef();

  const size = useSize(ref);

  useEffect(() => {
    if (size?.width) {
      const calcValue = Math.floor(size?.width / 80) - 3;

      setVisibleTiles(calcValue < 1 ? 0 : calcValue);
    }
  }, [size]);

  useEffect(() => {
    // default scroll event triiger to close the modal on scroll
    const handleScroll = (event) => {
      const target = event.target;
      if (!target.classList.contains('no-scroll-propagation')) {
        if (
          target.scrollHeight > target.clientHeight ||
          target.scrollWidth > target.clientWidth
        ) {
          setExpand(false);
        }
      }
    };
    document.addEventListener('scroll', handleScroll, true);

    return () => {
      document.removeEventListener('scroll', handleScroll, true);
    };
  }, []);

  const handleDataChange = (data, action, clickedOption = null) => {
    if (onChange) {
      onChange({
        target: {
          value: data,
          name,
          type: 'multiselect',
          action,
          clickedOption,
        },
      });
    }
  };

  const handleSelectAll = () => {
    if (isGroup) {
      const availableGroupOptions = allGroupOptions.filter(
        (o) => o.value !== '+'
      );
      const isSelectedAll =
        availableGroupOptions.filter((o) => !o.isDisabled).length ===
          value.length && availableGroupOptions.length === value.length;

      if (onChange) {
        onChange({
          target: {
            value: isSelectedAll
              ? []
              : availableGroupOptions.filter((opt) => !opt.isDisabled),
            name,
            type: 'multiselect',
            action: 'selectAll',
            clickedOption: null,
          },
        });
      }
    } else {
      const availableOptions = options.filter((o) => o.value !== '+');
      const isSelectedAll =
        availableOptions.filter((o) => !o.isDisabled).length === value.length &&
        availableOptions.length === value.length;

      if (onChange) {
        onChange({
          target: {
            value: isSelectedAll
              ? []
              : availableOptions.filter((opt) => !opt.isDisabled),
            name,
            type: 'multiselect',
            action: 'selectAll',
            clickedOption: null,
          },
        });
      }
    }
  };

  const updatePositon = () => {
    const rect = ref.current?.getBoundingClientRect();
    const availableScreenBelow = window?.screen?.availHeight - rect?.y;
    const cond = availableScreenBelow > 300;
    const obj = { top: rect?.top + rect?.height + 5 };
    if (!cond) {
      obj.top = rect.y - 5;
      obj.transform = `translate(0, -100%)`;
    }
    setStyles({
      width: rect?.width,
      left: rect?.left,
      ...obj,
    });
  };

  return (
    <div
      className={`${preferredOptions?.length > 0 ? 'min-w-[16rem]' : 'min-w-[120px]'} w-full ${
        disabled ? 'pointer-events-none rounded-lg' : ''
      } ${expand ? 'z-10' : ''} ${className}`}
      ref={ref}
    >
      <section
        tabIndex={-1}
        onFocus={() => {
          updatePositon();
          setExpand(expanded ? true : true);
        }}
        onBlur={() => {
          if (!isMouseOver) setExpand(expanded ? true : false);
        }}
        onClick={() => {
          if (!expand) {
            updatePositon();
            setExpand(true);
          }
        }}
        onMouseOver={() => setIsMouseOver(true)}
        onMouseLeave={() => setIsMouseOver(false)}
        className={`w-full h-full rounded-lg bg-white overflow-hidden border flex items-center ${isSingle ? 'justify-between' : ''} border-[#C8CEE1] ${innerClassname}`}
      >
        {!isSingle && value?.length > 0 ? (
          <>
            {value?.slice(0, visibleTiles)?.map((val, vIdx) => (
              <p
                key={vIdx}
                className="relative min-w-[70px] max-w-[70px] ml-2 text-[#0070FF] px-2 py-1 text-xs bg-[rgba(64,133,237,0.25)] rounded-lg"
              >
                <TruncateString length={5}>{val.label}</TruncateString>
                <XIcon
                  className="h-3 w-3 absolute top-1/2 right-[6px] -translate-y-1/2 cursor-pointer hover:text-red-600"
                  onClick={() =>
                    handleDataChange(
                      value.filter((item) => item.value !== val.value),
                      'remove',
                      null
                    )
                  }
                />
              </p>
            ))}
            {value?.length > visibleTiles ? (
              <p className="relative min-w-[50px] max-w-[50px] ml-2 text-[#0070FF] px-2 py-1 my-[2px] text-xs bg-[rgba(64,133,237,0.25)] rounded-lg text-center cursor-pointer">
                <TruncateString length={5}>
                  +{value?.length - visibleTiles}
                </TruncateString>
              </p>
            ) : null}
          </>
        ) : null}
        {isSingle && value?.length === 1 && (
          <p className="px-2 py-2 text-xs">{value?.[0]?.label}</p>
        )}
        <input
          className={`w-full h-full border-none bg-transparent outline-none pl-3 py-[0.3rem] flex justify-between items-center`}
          value={search}
          onChange={(e) => {
            setSearch(e.target.value);
            if (onSearch) {
              onSearch(e.target.value);
            }
          }}
          placeholder={placeholder}
        />
        <section
          className={`h-full flex justify-evenly items-center ${
            value?.length > 0
              ? 'min-w-[50px] max-w-[50px] '
              : 'min-w-[35px] max-w-[35px] '
          } ${!isSingle ? 'ml-auto' : ''}`}
        >
          {!isSingle && value?.length > 0 ? (
            <XIcon
              className={`h-4 w-4 cursor-pointer`}
              onClick={() => handleDataChange([], 'removeAll', null)}
            />
          ) : null}
          {isLoading ? (
            <LoaderCircle className="h-4 w-4 animate-spin text-gray-400" />
          ) : (
            <>
              {!expanded && (
                <ChevronDown
                  className={`h-4 w-4 transition-[transform] duration-100 ${
                    expand ? 'rotate-180' : ''
                  }`}
                  onClick={() => setExpand((prev) => !prev)}
                />
              )}
            </>
          )}
        </section>
        {expand ? (
          <div
            tabIndex={0}
            autoFocus
            ref={dropdownRef}
            style={styles}
            className={`no-scroll-propagation fixed z-10 rounded-md bg-white ${!userOptions ? 'border max-h-40' : 'max-h-64'} px-2 py-1.5 overflow-y-scroll overflow-x-hidden`}
          >
            {/* Add Option Moves out / Acc to logic it is not under the options.length condition */}
            <ul>
              {' '}
              {AddOption && handleAddFunction && (
                <li
                  onClick={handleAddFunction}
                  className="w-full py-0.5 px-2 hover:bg-blue-light rounded-[4px] cursor-pointer flex gap-x-2"
                >
                  {AddOption}
                </li>
              )}
            </ul>
            {options?.length > 0 ? (
              <>
                <ul className="text-sm">
                  {enableSelectAll && (
                    <li
                      className="w-full py-2 px-2 hover:bg-blue-light rounded-[4px] cursor-pointer flex gap-x-2"
                      onClick={handleSelectAll}
                    >
                      <input
                        type="checkbox"
                        className="pointer-events-none"
                        checked={
                          value?.length ===
                            (isGroup
                              ? allGroupOptions?.filter((o) => !o.isDisabled)
                                  ?.length
                              : options?.filter((o) => !o.isDisabled)
                                  ?.length) || false
                        }
                        readOnly
                      />
                      Select all
                    </li>
                  )}
                  {preferredOptions?.map((opt, oIdx) => {
                    const option = opt;
                    if (
                      doSearch &&
                      !option?.label
                        ?.toLowerCase()
                        ?.includes(search.toLowerCase())
                    )
                      return null;

                    if (option?.options?.length > 0)
                      return (
                        <Fragment key={oIdx}>
                          <li className="pl-2 mt-2 text-xs text-gray-600">
                            {option.label}
                          </li>
                          {option?.options?.map((gOpt, gIdx) => {
                            return (
                              <LiComp
                                key={gIdx}
                                option={gOpt}
                                setSearch={setSearch}
                                handleDataChange={handleDataChange}
                                closeOnSelect={closeOnSelect}
                                value={value}
                                setExpand={setExpand}
                                trimmedOptionLength={trimmedOptionLength}
                                getOptionTooltip={getOptionTooltip}
                              />
                            );
                          })}
                        </Fragment>
                      );
                    if (userOptions) {
                      return (
                        <UserOptions
                          key={oIdx}
                          option={option}
                          value={value}
                          setSearch={setSearch}
                          handleDataChange={handleDataChange}
                        />
                      );
                    }
                    return (
                      <LiComp
                        key={oIdx}
                        option={option}
                        setSearch={setSearch}
                        handleDataChange={handleDataChange}
                        closeOnSelect={closeOnSelect}
                        value={value}
                        setExpand={setExpand}
                        trimmedOptionLength={trimmedOptionLength}
                        isPreferred={true}
                        isSingle={isSingle}
                        getOptionTooltip={getOptionTooltip}
                      />
                    );
                  })}
                  {options?.map((opt, oIdx) => {
                    const option = opt;
                    if (
                      doSearch &&
                      !option?.label
                        ?.toLowerCase()
                        ?.includes(search.toLowerCase())
                    )
                      return null;

                    if (option?.options?.length > 0)
                      return (
                        <Fragment key={oIdx}>
                          <li className="pl-2 mt-2 text-xs text-gray-600">
                            {option.label}
                          </li>
                          {option?.options?.map((gOpt, gIdx) => {
                            return (
                              <LiComp
                                key={gIdx}
                                option={gOpt}
                                setSearch={setSearch}
                                handleDataChange={handleDataChange}
                                closeOnSelect={closeOnSelect}
                                value={value}
                                setExpand={setExpand}
                                trimmedOptionLength={trimmedOptionLength}
                                getOptionTooltip={getOptionTooltip}
                              />
                            );
                          })}
                        </Fragment>
                      );
                    if (userOptions) {
                      return (
                        <UserOptions
                          key={oIdx}
                          option={option}
                          value={value}
                          setSearch={setSearch}
                          handleDataChange={handleDataChange}
                        />
                      );
                    }
                    return (
                      <LiComp
                        key={oIdx}
                        option={option}
                        setSearch={setSearch}
                        handleDataChange={handleDataChange}
                        closeOnSelect={closeOnSelect}
                        value={value}
                        setExpand={setExpand}
                        trimmedOptionLength={trimmedOptionLength}
                        isSingle={isSingle}
                        getOptionTooltip={getOptionTooltip}
                      />
                    );
                  })}
                </ul>
              </>
            ) : (
              <p>No Options</p>
            )}
          </div>
        ) : null}
      </section>
    </div>
  );
};

export default MultiSelect;
