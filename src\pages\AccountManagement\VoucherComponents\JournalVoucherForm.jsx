import { ArrowLeftOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, DatePicker, Form } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { TiDelete } from 'react-icons/ti';
import { toast } from 'react-toastify';
import Input from '../../../components/global/components/Input';
import SelectV2 from '../../../components/global/components/SelectV2';
import Table from '../../../components/global/components/Table';
import Textarea from '../../../components/global/components/Textarea';
import { renderFieldsBasedOnType } from '../../../helperFunction';
import usePrefixIds from '../../../hooks/usePrefixIds';
import { useGetAllJournalsQuery } from '../../../slices/AccountManagement/journalApiSlice';
import { useQueryMasterAccountsForOptionsQuery } from '../../../slices/AccountManagement/masterAccountApiSlice';
import {
  useCreateVoucherMutation,
  useEditVoucherMutation,
  useGetVoucherByIdQuery,
} from '../../../slices/AccountManagement/voucherApiSlice';
import { useGetAllcustomerQuery } from '../../../slices/customerDataSlice';
import { useLazyQueryTemplateByIdQuery } from '../../../slices/dsahboardTemplateApiSlice';
import { useGetAllVendorsForOptionsQuery } from '../../../slices/vendorApiSlice';

const JournalVoucherForm = ({ props }) => {
  const { setOpenModal, editData, setEditData } = props;
  const [editVoucher] = useEditVoucherMutation();
  const { data: vendors } = useGetAllVendorsForOptionsQuery();
  const { data: customers } = useGetAllcustomerQuery();
  const { data: masterAccounts } = useQueryMasterAccountsForOptionsQuery();
  const { data: voucher } = useGetVoucherByIdQuery(
    { id: editData?._id },
    { skip: editData?._id === undefined }
  );
  const isEditing = editData?._id !== undefined;
  const { data: journals, isLoading: isJournalsLoading } =
    useGetAllJournalsQuery();

  const [createVoucher] = useCreateVoucherMutation();

  const [additionalFields, setAdditionalFields] = useState(null);
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
  });

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'journalVoucherId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
    setIdData: setFormData,
  });
  const [vendorCustomerOptions, setVendorCustomerOptions] = useState([]);
  const [accounts, setAccounts] = useState([]);

  useEffect(() => {
    const path = '/accountmanagement/voucher/journalVoucher';
    getTemplates({ path });
  }, [getTemplates]);

  useEffect(() => {
    if (voucher?._id && voucher?.journalVoucherData?.additionalFields) {
      setAdditionalFields(voucher.journalVoucherData.additionalFields);
      setSelectedTemplate(voucher.journalVoucherData.additionalFields);
    } else if (templatesData && Array.isArray(templatesData)) {
      const defaultTemplate =
        templatesData.find((t) => t?.name?.startsWith('Default')) ||
        templatesData?.[0];
      if (defaultTemplate) {
        setAdditionalFields(defaultTemplate);
        setSelectedTemplate(defaultTemplate);
      }
    }
  }, [
    voucher?._id,
    voucher?.journalVoucherData?.additionalFields,
    templatesData,
  ]);

  const changeHandler = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  useEffect(() => {
    if (formData?.journal?.length === 24 && editData?._id === undefined) {
      const selectedJournal = journals?.find(
        (journal) => journal._id === formData.journal
      );
      if (selectedJournal?.accounts?.length > 0) {
        const tempAccounts = selectedJournal?.accounts?.map((account) => {
          if (account?.customer?._id !== undefined) {
            return {
              account: account?.account?._id,
              description: account?.description || '',
              customer: account?.customer?._id,
              debit: account?.debit || '',
              credit: account?.credit || '',
            };
          } else if (account?.vendor?._id !== undefined) {
            return {
              account: account?.account?._id,
              description: account?.description || '',
              vendor: account?.vendor?._id,
              debit: account?.debit || '',
              credit: account?.credit || '',
            };
          } else {
            return {
              account: account?.account?._id,
              description: account?.description || '',
              debit: account?.debit || '',
              credit: account?.credit || '',
            };
          }
        });
        setAccounts(tempAccounts);
      }
    }
  }, [formData?.journal, editData, journals]);

  useEffect(() => {
    let options = [];
    if (vendors?.length > 0) {
      let temp = vendors.map((vendor) => ({
        name: vendor.name,
        value: vendor._id,
      }));
      options = [...options, ...temp];
    }
    if (customers?.customers?.length > 0) {
      let temp = customers.customers.map((customer) => ({
        name: customer.name,
        value: customer._id,
      }));
      options = [...options, ...temp];
    }
    setVendorCustomerOptions(options);
  }, [vendors, customers]);

  useEffect(() => {
    if (voucher?._id) {
      let date =
        voucher?.date !== undefined
          ? new Date(voucher?.date)?.toISOString()?.split('T')?.[0]
          : '';
      setFormData({
        date: date,
        journalVoucherId: voucher?.journalVoucherId,
        voucherType: voucher?.voucherType,
        journal: voucher?.journalVoucherData?.journal?._id,
        remarks: voucher?.remarks,
        referenceId: voucher?.journalVoucherData?.referenceId,
        additionalFields: voucher?.journalVoucherData?.additionalFields || null,
      });
      const tempAccounts = voucher?.journalVoucherData?.accounts?.map(
        (account) => {
          if (account?.customer?._id !== undefined) {
            return {
              account: account?.account?._id,
              description: account?.description || '',
              customer: account?.customer?._id,
              debit: account?.debit || '',
              credit: account?.credit || '',
            };
          } else if (account?.vendor?._id !== undefined) {
            return {
              account: account?.account?._id,
              description: account?.description || '',
              vendor: account?.vendor?._id,
              debit: account?.debit || '',
              credit: account?.credit || '',
            };
          } else {
            return {
              account: account?.account?._id,
              description: account?.description || '',
              debit: account?.debit || '',
              credit: account?.credit || '',
            };
          }
        }
      );
      setAccounts(tempAccounts);
    }
  }, [voucher]);

  const addAccount = () => {
    setAccounts((prev) => [
      ...prev,
      {
        account: '',
        description: '',
        debit: '',
        credit: '',
      },
    ]);
  };

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }

    if (fieldValue === '+') {
      setDropdownIdx(idx);
      setTemplateDropDownModal(true);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field) => {
          if (field?.fieldName === fieldName) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );
      setAdditionalFields((prev) => ({
        ...prev,
        templateData: updatedAdditionalFields,
      }));
      setFormData((prev) => ({
        ...prev,
        additionalFields: {
          ...prev.additionalFields,
          templateData: updatedAdditionalFields,
          name: additionalFields?.name,
        },
      }));
    }
  };

  const handleSubmit = async () => {
    let transformedAccounts = accounts?.map((account) => {
      let foundAccount = masterAccounts?.find(
        (elem) => elem._id === account?.account
      );
      return {
        ...account,
        accountName: foundAccount?.accountDescription?.name,
      };
    });
    let obj = {
      ...formData,
      accounts: transformedAccounts,
      additionalFields: additionalFields || formData?.additionalFields || null,
    };
    let res;
    if (editData?._id) {
      res = await editVoucher({ data: { updateData: obj, id: editData?._id } });
    } else {
      res = await createVoucher({
        data: { ...obj, voucherType: 'journalVoucher' },
      });
    }
    if (!res?.error) {
      setOpenModal(false);
      setFormData({});
      toast.success(
        `Journal Voucher ${editData?._id ? 'Updated' : 'Created'} successfully`
      );
    } else {
      toast.error(
        'Faced an error while creating voucher, please reload and try again.'
      );
    }
  };

  const calculateDebit = () => {
    let sum = 0;
    accounts?.forEach((elem) => (sum = sum + parseInt(elem?.debit)));
    return sum;
  };

  const calculateCredit = () => {
    let sum = 0;
    accounts?.forEach((elem) => (sum = sum + parseInt(elem?.credit)));
    return sum;
  };

  const totalDifference = () => {
    return calculateDebit() - calculateCredit();
  };

  return (
    <>
      <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
        {/* Header */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                setOpenModal(false);
                setEditData({});
              }}
              type="text"
              size="small"
              className="hover:bg-gray-200"
            />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-0">
                {isEditing ? 'Edit' : 'Create'} Journal Voucher
              </h2>
              <p className="text-sm text-gray-600 mb-0">
                {isEditing
                  ? 'Update journal voucher information'
                  : 'Create a new journal voucher'}
              </p>
            </div>
          </div>
        </div>

        <Form layout="vertical" onFinish={handleSubmit}>
          <div className="p-4 space-y-4">
            {/* Basic Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Journal Voucher ID
                  </label>
                  {isEditing ? (
                    <p className="text-sm text-gray-900">
                      <Input
                        disabled
                        className="text-sm bg-gray-50"
                        value={formData?.journalVoucherId}
                      />
                    </p>
                  ) : (
                    <IdGenComp {...idCompData} />
                  )}
                </div>

                <div className="space-y-1">
                  <label className="block mb-1 text-sm text-gray-500 font-medium">
                    Choose Template
                  </label>
                  <SelectV2
                    options={templatesData?.map((template) => ({
                      value: template?._id,
                      name: template?.name,
                    }))}
                    value={selectedTemplate?._id}
                    onChange={(e) => {
                      const template = templatesData.find(
                        (t) => t._id === e.target.value
                      );
                      if (selectedTemplate?._id === e.target.value) {
                        return;
                      }
                      setAdditionalFields(template);
                      setSelectedTemplate(template);
                      if (selectedTemplate?.idIndex === template.idIndex) {
                        return;
                      }
                    }}
                  />
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Date
                  </label>
                  <DatePicker
                    format={'DD-MM-YYYY'}
                    className="text-sm placeholder:text-gray-400 w-full"
                    size="middle"
                    name="date"
                    value={formData?.date ? dayjs(formData.date) : null}
                    placeholder="Select date"
                    onChange={(e) =>
                      changeHandler({
                        target: {
                          name: 'date',
                          value: e?.format('YYYY-MM-DD'),
                        },
                      })
                    }
                  />
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Reference ID
                  </label>
                  <Input
                    name="referenceId"
                    placeholder="Enter reference ID"
                    value={formData.referenceId}
                    onChange={(e) => changeHandler(e)}
                    className="text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Journal Selection */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Journal Selection
              </h3>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">
                  Select Journal
                </label>
                <SelectV2
                  name="journal"
                  placeholder="Select journal"
                  value={formData.journal}
                  isLoading={isJournalsLoading}
                  options={
                    journals?.map((elem) => ({
                      name: elem?.journalId,
                      value: elem._id,
                    })) || []
                  }
                  onChange={(e) => changeHandler(e)}
                  className="text-sm"
                />
              </div>
            </div>
            {/* Account Details */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-sm font-medium text-gray-700">
                  Account Details
                </h3>
                <Button
                  size="small"
                  onClick={addAccount}
                  type="primary"
                  className="text-sm px-3 py-1 h-7"
                >
                  + Add Account
                </Button>
              </div>
              <div className="overflow-x-auto border border-gray-200 rounded-lg">
                <Table className="min-w-full border-collapse">
                  <Table.Head>
                    <Table.Row className="bg-gray-50">
                      <Table.Th className="text-sm font-medium text-gray-600 px-3 py-2 border-r border-gray-200">
                        Account
                      </Table.Th>
                      <Table.Th className="text-sm font-medium text-gray-600 px-3 py-2 border-r border-gray-200">
                        Description
                      </Table.Th>
                      <Table.Th className="text-sm font-medium text-gray-600 px-3 py-2 border-r border-gray-200">
                        Party
                      </Table.Th>
                      <Table.Th className="text-sm font-medium text-gray-600 px-3 py-2 border-r border-gray-200">
                        Debit
                      </Table.Th>
                      <Table.Th className="text-sm font-medium text-gray-600 px-3 py-2 border-r border-gray-200">
                        Credit
                      </Table.Th>
                      <Table.Th className="text-sm font-medium text-gray-600 px-3 py-2 w-8"></Table.Th>
                    </Table.Row>
                  </Table.Head>
                  <Table.Body>
                    {accounts?.map((account, index) => (
                      <Table.Row
                        key={index}
                        className="border-b border-gray-200 hover:bg-gray-50"
                      >
                        <Table.Td className="px-3 py-2 border-r border-gray-200">
                          <SelectV2
                            name="account"
                            placeholder="Select account"
                            value={account.account}
                            options={masterAccounts?.map((item) => ({
                              name:
                                item.accountDescription?.name ||
                                item.accountNumber,
                              value: item._id,
                            }))}
                            onChange={(e) => {
                              const newAccounts = [...accounts];
                              newAccounts[index].account = e.target.value;
                              setAccounts(newAccounts);
                            }}
                            menuPosition="fixed"
                            size="small"
                          />
                        </Table.Td>
                        <Table.Td className="px-3 py-2 border-r border-gray-200">
                          <Input
                            name="description"
                            placeholder="Description"
                            value={account.description}
                            onChange={(e) => {
                              const newAccounts = [...accounts];
                              newAccounts[index].description = e.target.value;
                              setAccounts(newAccounts);
                            }}
                            className="text-sm min-w-[100px]"
                          />
                        </Table.Td>
                        <Table.Td className="px-3 py-2 border-r border-gray-200">
                          <SelectV2
                            name="vendorCustomer"
                            placeholder="Select party"
                            value={account.vendor || account?.customer}
                            options={vendorCustomerOptions}
                            onChange={(e) => {
                              const newAccounts = [...accounts];
                              let foundVendor = vendors?.find(
                                (elem) => elem?._id === e.target.value
                              );
                              let foundCustomer = customers?.customers?.find(
                                (elem) => elem?._id === e.target.value
                              );
                              if (foundVendor?._id) {
                                newAccounts[index].vendor = e.target.value;
                              }
                              if (foundCustomer?._id) {
                                newAccounts[index].customer = e.target.value;
                              }
                              setAccounts(newAccounts);
                            }}
                            menuPosition="fixed"
                            size="small"
                          />
                        </Table.Td>
                        <Table.Td className="px-3 py-2 border-r border-gray-200">
                          <Input
                            type="number"
                            name="debit"
                            placeholder="0.00"
                            value={account.debit}
                            onChange={(e) => {
                              const newAccounts = [...accounts];
                              newAccounts[index].debit = e.target.value;
                              setAccounts(newAccounts);
                            }}
                            className="text-sm min-w-[80px]"
                          />
                        </Table.Td>
                        <Table.Td className="px-3 py-2 border-r border-gray-200">
                          <Input
                            type="number"
                            name="credit"
                            placeholder="0.00"
                            value={account.credit}
                            onChange={(e) => {
                              const newAccounts = [...accounts];
                              newAccounts[index].credit = e.target.value;
                              setAccounts(newAccounts);
                            }}
                            className="text-sm min-w-[80px]"
                          />
                        </Table.Td>
                        <Table.Td className="px-3 py-2">
                          <button
                            onClick={() => {
                              setAccounts((prev) =>
                                prev?.filter(
                                  (_, elemIndex) => elemIndex !== index
                                )
                              );
                            }}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 rounded transition-colors"
                          >
                            <TiDelete className="text-base" />
                          </button>
                        </Table.Td>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
              {/* Summary */}
              <div className="mt-3 pt-3 border-t border-gray-200 flex justify-end gap-4">
                <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600 min-w-[10rem]">
                      Total Debit:
                    </span>
                    <span className="font-medium text-red-600">
                      ₹ {calculateDebit() || 0}
                    </span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600 min-w-[10rem]">
                      Total Credit:
                    </span>
                    <span className="font-medium text-green-600">
                      ₹ {calculateCredit() || 0}
                    </span>
                  </div>
                  <hr className="border-gray-300" />
                  <div className="flex justify-between items-center text-sm font-semibold">
                    <span className="text-gray-700">Difference:</span>
                    <span
                      className={`${totalDifference() === 0 ? 'text-green-600' : 'text-red-600'}`}
                    >
                      ₹ {totalDifference() || 0}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Template Details */}
            {additionalFields?.templateData?.length > 0 && (
              <div className="bg-white border border-gray-200 rounded-lg p-3">
                <h3 className="text-sm font-medium text-gray-700 mb-3">
                  Template Details
                </h3>
                <section className="w-full">
                  {renderFieldsBasedOnType(
                    additionalFields,
                    handleInputChange,
                    templateDropDownModal,
                    setTemplateDropDownModal,
                    setAdditionalFields,
                    newOptionStatus,
                    setNewOptionStatus,
                    dropdownIdx,
                    setDropdownIdx
                  )}
                </section>
              </div>
            )}

            {/* Remarks */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Remarks
              </h3>
              <Textarea
                name="remarks"
                value={formData.remarks}
                onChange={(e) => changeHandler(e)}
                rows={3}
                placeholder="Enter additional remarks or comments"
                className="text-sm resize-none"
              />
            </div>
          </div>

          {/* Footer Actions */}
          <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
            <div className="flex items-center justify-end">
              <Button
                htmlType="submit"
                type="primary"
                size="small"
                className="text-sm px-4 py-1 h-8"
              >
                {isEditing ? 'Update' : 'Save'} Journal Voucher
              </Button>
            </div>
          </div>
        </Form>
      </div>
    </>
  );
};

export default JournalVoucherForm;
