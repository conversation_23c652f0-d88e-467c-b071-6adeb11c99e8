import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Button, Space, Table, Tag, Tooltip, Typography } from 'antd';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';
import { mobileWidth } from '../../../helperFunction';
import { useDeleteVoucherMutation } from '../../../slices/AccountManagement/voucherApiSlice';
import { customConfirm } from '../../../utils/customConfirm';

const { Text } = Typography;

const PurchaseVoucherTable = ({
  rows,
  isLoading,
  checkedRows,
  handleSelectAll,
  selectAll,
  handleCheckBoxChange,
  setOpenSidebar,
  setVoucherId,
  onEdit,
}) => {
  const [deleteVoucher] = useDeleteVoucherMutation();
  const isMobile = useMediaQuery({ query: mobileWidth });

  const handleDelete = async (record) => {
    const confirm = await customConfirm(
      'Are you sure you want to delete this voucher?',
      'delete'
    );
    if (!confirm) return;
    try {
      const res = await deleteVoucher({ data: { id: record._id } });
      if (res.error) {
        toast.error('Failed to delete voucher. Please reload and try again.');
      } else {
        toast.success('Voucher deleted successfully');
      }
    } catch (error) {
      toast.error('An error occurred while deleting the voucher.');
    }
  };

  const columns = [
    !isMobile && {
      title: (
        <div>
          {rows?.length > 0 && (
            <label className="flex items-center whitespace-nowrap">
              <input
                type="checkbox"
                checked={selectAll}
                onChange={handleSelectAll}
                style={{ marginRight: 8 }}
              />
              Select All ({checkedRows?.length || 0})
            </label>
          )}
        </div>
      ),
      dataIndex: 'selection',
      key: 'selection',
      width: 130,
      render: (_, record) => (
        <input
          type="checkbox"
          checked={checkedRows?.includes(record)}
          onChange={(e) => {
            handleCheckBoxChange(e, record);
            e.stopPropagation();
          }}
        />
      ),
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: (a, b) => new Date(a.date) - new Date(b.date),
    },
    {
      title: 'Purchase Voucher ID',
      dataIndex: 'purchaseVoucherId',
      key: 'purchaseVoucherId',
      width: 180,
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => {
            setVoucherId(record._id);
            setOpenSidebar(true);
          }}
          style={{ padding: 0, height: 'auto' }}
        >
          <Text strong style={{ color: '#1890ff' }}>
            {text || '-'}
          </Text>
        </Button>
      ),
    },
    {
      title: 'Vendor',
      dataIndex: ['purchaseVoucherData', 'vendor', 'name'],
      key: 'vendor',
      width: 200,
      render: (_, record) => (
        <div
          onClick={() => {
            setVoucherId(record._id);
            setOpenSidebar(true);
          }}
          className="cursor-pointer"
        >
          <Text>
            {record?.purchaseVoucherData?.vendor?.name ||
              record?.vendor?.name ||
              '-'}
          </Text>
        </div>
      ),
    },
    {
      title: 'Ledger Type',
      dataIndex: ['purchaseVoucherData', 'ledgerType', 'name'],
      key: 'ledgerType',
      width: 150,
      render: (_, record) => (
        <Tag color="blue">
          {record?.purchaseVoucherData?.ledgerType?.name ||
            record?.ledgerType?.name ||
            '-'}
        </Tag>
      ),
    },
    {
      title: 'Items Count',
      key: 'itemsCount',
      width: 120,
      render: (_, record) => (
        <Tag color="green">
          {record?.purchaseVoucherData?.items?.length || 0} items
        </Tag>
      ),
    },
    {
      title: 'Remarks',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 200,
      render: (remark) => {
        if (!remark) return '-';
        if (remark.length > 50) {
          return (
            <Tooltip title={remark} placement="topLeft">
              <Text ellipsis style={{ maxWidth: 180, display: 'block' }}>
                {remark}
              </Text>
            </Tooltip>
          );
        }
        return remark;
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            onClick={() => {
              if (onEdit) {
                onEdit(record);
              }
            }}
            style={{ color: '#52c41a' }}
          />
          <Button
            type="text"
            icon={<DeleteOutlined />}
            size="small"
            danger
            onClick={() => handleDelete(record)}
          />
        </Space>
      ),
    },
  ].filter(Boolean);

  return (
    <div className="purchase-voucher-table-container">
      <Table
        columns={columns}
        dataSource={rows || []}
        rowKey="_id"
        loading={isLoading}
        pagination={false}
        scroll={{ x: 1200 }}
      />
    </div>
  );
};

export default PurchaseVoucherTable;
