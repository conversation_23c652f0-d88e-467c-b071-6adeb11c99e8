import {
  BankOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  IdcardOutlined,
  PhoneOutlined,
  UserOutlined,
  WhatsAppOutlined,
} from '@ant-design/icons';
import {
  FaBusinessTime,
  FaCalendarCheck,
  FaCalendarDay,
  FaClock,
  FaEye,
  FaMoon,
} from 'react-icons/fa';

import { Button, Card, Modal, Table, Tabs, Tag, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { LuFiles } from 'react-icons/lu';
import MediaViewer from '../../../components/global/components/MediaViewer';
import RightSidebar from '../../../components/global/components/RightSidebar';
import { useLazyGetMediaByIdQuery } from '../../../slices/mediaSlice';
import { useLazyGetUserByIdQuery } from '../../../slices/userApiSlice';
const { Title, Text } = Typography;

const GeneralDetailsRow = ({ name, Icon, value, color, className }) => {
  return (
    <div
      className={`flex items-center justify-between border-b pb-2 ${className}`}
    >
      <div className="flex items-center gap-2">
        <Icon className={`${color} text-xl`} />
        <span className="font-medium text-gray-600">{name}</span>
      </div>
      <span className="text-gray-800 font-medium">{value}</span>
    </div>
  );
};

const tabItems = [
  {
    label: `General`,
    key: 'general',
  },
  {
    label: `Leaves`,
    key: 'leaves',
  },
  {
    label: `Transactions`,
    key: 'transactions',
  },
];

const ViewSidebar = ({
  openSideBar,
  setOpenSideBar,
  data,
  startDate,
  endDate,
}) => {
  const [getMediaById] = useLazyGetMediaByIdQuery();
  const [getUserById] = useLazyGetUserByIdQuery();

  const [transactions, setTransactions] = useState([]);
  const [leaves, setLeaves] = useState([]);
  const [selectedTab, setSelectedTab] = useState('general');
  const [showMedia, setShowMedia] = useState(false);
  const [media, setMedia] = useState([]);
  const [selectedMedia, setSelectedMedia] = useState({ data: '', type: '' });
  const [record, setRecord] = useState({});

  const sortByDate = (array) => {
    return array.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA - dateB; // For ascending (oldest first)
    });
  };

  function filterDatesInRange(dataArray, startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);

    return dataArray.filter((item) => {
      if (item?.date === '') {
        return true;
      } else {
        const itemDate = new Date(item.date);
        return itemDate >= start && itemDate <= end;
      }
    });
  }

  function getAttendanceBetweenDates(attendance, startDate, endDate) {
    const result = {};

    const start = new Date(startDate);
    const end = new Date(endDate);

    for (const date in attendance) {
      const currentDate = new Date(date);

      // Normalize time for accurate comparison
      currentDate.setHours(0, 0, 0, 0);
      start.setHours(0, 0, 0, 0);
      end.setHours(0, 0, 0, 0);

      if (currentDate >= start && currentDate <= end) {
        result[date] = attendance[date];
      }
    }

    return result;
  }

  useEffect(() => {
    if (data) {
      let attendance = {};
      let bonusArray = [];
      let advanceArray = [];
      let deductionArray = [];
      let adHocArray = [];
      let reimbursements = [];

      bonusArray = filterDatesInRange(
        data?.bonusData || [],
        startDate,
        endDate
      );
      deductionArray = filterDatesInRange(
        data?.deductionData || [],
        startDate,
        endDate
      );
      advanceArray = filterDatesInRange(
        data?.advanceData || [],
        startDate,
        endDate
      );
      adHocArray = filterDatesInRange(
        data?.adHocData || [],
        startDate,
        endDate
      );
      reimbursements = filterDatesInRange(
        data?.reimbursements || [],
        startDate,
        endDate
      );
      attendance = getAttendanceBetweenDates(
        data?.attendance,
        startDate,
        endDate
      );
      setRecord({
        ...data,
        bonusData: bonusArray,
        deductionData: deductionArray,
        advanceData: advanceArray,
        adHocData: adHocArray,
        reimbursements,
        attendance,
      });
    }
  }, [data, endDate, startDate]);

  useEffect(() => {
    let temp = [];
    if (record?.bonusData?.length > 0) {
      for (let i of record?.bonusData) {
        temp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          credit: true,
          type: 'Bonus',
        });
      }
    }
    if (record?.deductionData?.length > 0) {
      for (let i of record?.deductionData) {
        temp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          credit: false,
          type: 'Deduction',
        });
      }
    }
    if (record?.advanceData?.length > 0) {
      for (let i of record?.advanceData) {
        temp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          credit: true,
          type: 'Advance',
        });
      }
    }
    if (record?.adHocData?.length > 0) {
      for (let i of record?.adHocData) {
        temp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          credit: true,
          media: i?.media,
          type: 'Ad Hoc',
        });
      }
    }
    let sorted = sortByDate(temp);
    setTransactions(sorted);

    getLeavesInDateRange();
  }, [record]); //eslint-disable-line

  const getLeavesInDateRange = async () => {
    let tempLeaves = [];
    let attendanceDates = Object.keys(record?.attendance || {});
    for (let i of attendanceDates) {
      if (record?.attendance?.[i]?.isLeave === true) {
        let user;
        if (
          record?.attendance?.[i]?.leaveApprovedBy !== undefined &&
          record?.attendance?.[i]?.leaveApprovedBy !== null
        ) {
          user = await getUserById({
            id: record?.attendance?.[i]?.leaveApprovedBy,
          });
        }
        tempLeaves?.push({
          date: i,
          reason: record?.attendance?.[i]?.leaveReason,
          approvedBy: user?.data?.name,
        });
      }
    }
    let sortedLeaves = sortByDate(tempLeaves);
    setLeaves(sortedLeaves);
  };

  const leavesColumns = [
    {
      title: 'Date Of Leave',
      key: 'date',
      render: (_, record) => <Tag color="purple">{record?.date}</Tag>,
    },
    {
      title: 'Leave Reason',
      key: 'reason',
      render: (_, record) => <p>{record?.reason}</p>,
    },
    {
      title: 'ApprovedBy',
      key: 'approvedBy',
      render: (_, record) => <Tag color="blue">{record?.approvedBy}</Tag>,
    },
  ];

  const columns = [
    {
      title: 'Date',
      key: 'date',
      render: (_, record) => <Tag color="orange">{record?.date}</Tag>,
    },
    {
      title: 'Credit/Debit',
      key: 'creditDebit',
      render: (_, record) => (
        <p
          className={`${record?.credit ? 'text-green-500' : 'text-red-500'}`}
        >{`${record?.credit ? '+' : '-'} ₹${record?.amount}`}</p>
      ),
    },
    {
      title: 'Type',
      key: 'type',
      render: (_, record) => <Tag color="orange">{record?.type}</Tag>,
    },
    {
      title: 'Media',
      key: 'media',
      render: (_, record) => (
        <>
          {record?.media?.length > 0 && (
            <div
              className="p-[6px] rounded-[5px] bg-blue-500 hover:bg-blue-400 cursor-pointer w-fit relative"
              onClick={async () => {
                setShowMedia(true);
                let temp = [];
                for (let i of record?.media) {
                  if (i?.data === undefined) {
                    let med = await getMediaById({ id: i });
                    temp?.push(med?.data?.media);
                  } else {
                    temp?.push(i);
                  }
                }
                setMedia(temp);
              }}
            >
              <LuFiles className="text-lg text-white w-fit" />
              {record?.media?.length > 0 && (
                <span className="rounded-full py-[2px] px-[7px] text-[10px] text-white bg-red-500 absolute top-[-0.6rem] left-[1.2rem]">
                  {record?.media?.length}
                </span>
              )}
            </div>
          )}
        </>
      ),
    },
  ];

  const mediaColumns = [
    {
      title: 'Name',
      key: 'mediaName',
      render: (_, record) => <p>{record?.name}</p>,
    },
    {
      title: 'Type',
      key: 'mediaType',
      render: (_, record) => <Tag color="purple">{record?.type}</Tag>,
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <div className="flex items-center gap-2 justify-end">
          <FaEye
            onClick={() => {
              setSelectedMedia(record);
            }}
            className="text-2xl text-slate-300 hover:text-slate-400 cursor-pointer"
          />
        </div>
      ),
    },
  ];

  function getDaysWorkedThisMonth(attendance) {
    let daysWorked = 0;

    for (const date in attendance) {
      const entry = attendance[date];

      if (
        entry &&
        Object.keys(entry).length > 0 &&
        entry.isLeave !== true &&
        entry.login &&
        entry.logout
      ) {
        daysWorked++;
      }
    }

    return daysWorked;
  }

  function getHoursWorkedThisMonth(attendance) {
    let totalHours = 0;

    for (const date in attendance) {
      const entry = attendance[date];

      if (
        entry &&
        Object.keys(entry).length > 0 &&
        entry.isLeave !== true &&
        entry.login &&
        entry.logout
      ) {
        const login = new Date(entry.login);
        const logout = new Date(entry.logout);
        const diffMs = logout - login;

        if (diffMs > 0) {
          totalHours += diffMs / (1000 * 60 * 60); // ms to hours
        }
      }
    }

    return totalHours;
  }

  function getNightShiftsWorked(attendance) {
    let nightShiftCount = 0;

    for (const date in attendance) {
      const entry = attendance[date];

      if (
        entry &&
        Object.keys(entry).length > 0 &&
        entry.isLeave !== true &&
        entry.login &&
        entry.logout
      ) {
        const login = new Date(entry.login);
        const logout = new Date(entry.logout);

        const loginHour = login.getHours();
        const logoutHour = logout.getHours();

        // Login on or after 6 PM and logout on next day before 9 AM
        if (
          loginHour >= 18 &&
          (logout.getDate() !== login.getDate() || logoutHour < 9)
        ) {
          nightShiftCount++;
        }
      }
    }

    return nightShiftCount;
  }

  function getExtraHoursWorked(attendance) {
    let extraHours = 0;

    for (const date in attendance) {
      const entry = attendance[date];

      if (
        entry &&
        Object.keys(entry).length > 0 &&
        entry.isLeave !== true &&
        entry.login &&
        entry.logout
      ) {
        const login = new Date(entry.login);
        const logout = new Date(entry.logout);

        const workedMs = logout - login;
        const workedHours = workedMs / (1000 * 60 * 60);

        if (workedHours > record?.workingHours) {
          extraHours += workedHours - record?.workingHours;
        }
      }
    }

    return extraHours;
  }
  const closeMediaModal = () => {
    setShowMedia(false);
  };

  return (
    <>
      {selectedMedia?.data?.length !== 0 && openSideBar && (
        <MediaViewer media={selectedMedia} setMedia={setSelectedMedia} />
      )}
      <Modal
        title="AdHoc Media"
        onCancel={closeMediaModal}
        open={showMedia}
        width={800}
        footer={[
          <Button key="back" onClick={closeMediaModal}>
            Done
          </Button>,
        ]}
      >
        <div className="h-[40vh] overflow-y-scroll">
          <Table
            columns={mediaColumns}
            dataSource={media}
            rowKey={(_, index) => index}
            pagination={false}
            size="middle"
            scroll={{ x: true }}
            locale={{ emptyText: 'No Data added yet' }}
          />
        </div>
      </Modal>
      <RightSidebar
        openSideBar={openSideBar}
        setOpenSideBar={setOpenSideBar}
        title="Employee Details"
        scale={600}
      >
        <Tabs
          defaultActiveKey="workOrder"
          centered
          items={tabItems}
          onChange={(e) => {
            setSelectedTab(e);
          }}
        />
        {selectedTab === 'general' && (
          <>
            <div>
              <h4 className="my-2">General Details</h4>
              <div className="border rounded p-4">
                <GeneralDetailsRow
                  name={'Name'}
                  Icon={IdcardOutlined}
                  value={record?.name}
                  color={'text-indigo-500'}
                />
                <GeneralDetailsRow
                  name={'Email'}
                  Icon={IdcardOutlined}
                  value={record?.email}
                  color={'text-purple-500'}
                  className="mt-2"
                />
                <GeneralDetailsRow
                  name={'Gender'}
                  Icon={UserOutlined}
                  value={record?.gender}
                  color={'text-indigo-500'}
                  className="mt-2"
                />
                <GeneralDetailsRow
                  name={'Contact Number'}
                  Icon={PhoneOutlined}
                  value={record?.contactNumber}
                  color={'text-yellow-500'}
                  className="mt-2"
                />
                <GeneralDetailsRow
                  name={'Whatsapp Number'}
                  Icon={WhatsAppOutlined}
                  value={record?.whatsappNumber}
                  color={'text-green-500'}
                  className="mt-2"
                />
                <GeneralDetailsRow
                  name={'Working Hours'}
                  Icon={ClockCircleOutlined}
                  value={record?.workingHours}
                  color={'text-orange-500'}
                  className="mt-2"
                />
                <GeneralDetailsRow
                  name={'Fixed Salary'}
                  Icon={DollarOutlined}
                  value={record?.fixedSalary}
                  color={'text-lime-500'}
                  className="mt-2"
                />
              </div>
            </div>
            <div>
              <Card
                className="my-4"
                title={
                  <div className="flex items-center gap-2">
                    <BankOutlined className="text-primary" />
                    <Title level={5} className="!mb-0">
                      Bank Details
                    </Title>
                  </div>
                }
              >
                <div className="flex items-start px-6 py-2 border-b last:border-b-0 border-gray-100">
                  <Text className="w-48 text-gray-500 font-medium">
                    Bank Name
                  </Text>
                  <Text className="flex-1 text-gray-700 text-right">
                    {record?.bankName || '-'}
                  </Text>
                </div>
                <div className="flex items-start px-6 py-2 border-b last:border-b-0 border-gray-100">
                  <Text className="w-48 text-gray-500 font-medium">
                    Account Number
                  </Text>
                  <Text className="flex-1 text-gray-700 text-right">
                    {record?.accountNumber || '-'}
                  </Text>
                </div>
                <div className="flex items-start px-6 py-2 border-b last:border-b-0 border-gray-100">
                  <Text className="w-48 text-gray-500 font-medium">
                    IIFC Code
                  </Text>
                  <Text className="flex-1 text-gray-700 text-right">
                    {record?.iifcCode || '-'}
                  </Text>
                </div>
              </Card>
            </div>
            <div>
              <h4 className="my-2">Attendance Details</h4>
              <div className="border rounded p-4">
                <GeneralDetailsRow
                  name={'Leaves Assigned'}
                  Icon={FaCalendarDay}
                  value={record?.leavesAllowed}
                  color="text-blue-400"
                  tag={true}
                  tagColor="blue"
                />
                <GeneralDetailsRow
                  name={'Leaves Taken'}
                  Icon={FaCalendarCheck}
                  value={leaves?.length || 0}
                  color="text-red-400"
                  tag={true}
                  tagColor="red"
                  className="mt-2"
                />
                {openSideBar && (
                  <GeneralDetailsRow
                    name={'Hours Worked'}
                    Icon={FaClock}
                    value={getHoursWorkedThisMonth(record?.attendance)}
                    color="text-yellow-300"
                    tag={true}
                    tagColor="yellow"
                    className="mt-2"
                  />
                )}
                {openSideBar && (
                  <GeneralDetailsRow
                    name={'Days Worked'}
                    Icon={FaBusinessTime}
                    value={getDaysWorkedThisMonth(record?.attendance)}
                    color="text-green-300"
                    tag={true}
                    tagColor="green"
                    className="mt-2"
                  />
                )}
                {openSideBar && (
                  <GeneralDetailsRow
                    name={'Night Shifts Worked This Month'}
                    Icon={FaMoon}
                    value={getNightShiftsWorked(record?.attendance)}
                    color="text-purple-300"
                    tag={true}
                    tagColor="purple"
                    className="mt-2"
                  />
                )}
                {openSideBar && (
                  <GeneralDetailsRow
                    name={'Extra Hours Worked This Month'}
                    Icon={FaMoon}
                    value={getExtraHoursWorked(record?.attendance)}
                    color="text-purple-300"
                    tag={true}
                    tagColor="purple"
                    className="mt-2"
                  />
                )}
              </div>
            </div>
          </>
        )}
        {selectedTab === 'leaves' && (
          <>
            <Table
              columns={leavesColumns}
              dataSource={leaves}
              rowKey={(_, index) => index}
              pagination={false}
              size="middle"
              scroll={{ x: true }}
              locale={{ emptyText: 'No Data added yet' }}
            />
          </>
        )}
        {selectedTab === 'transactions' && (
          <>
            <Table
              columns={columns}
              dataSource={transactions}
              rowKey={(_, index) => index}
              pagination={false}
              size="middle"
              scroll={{ x: true }}
              locale={{ emptyText: 'No Data added yet' }}
            />
          </>
        )}
      </RightSidebar>
    </>
  );
};

export default ViewSidebar;
