import {
  BankOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  IdcardOutlined,
  PhoneOutlined,
  UserOutlined,
  WhatsAppOutlined,
} from '@ant-design/icons';
import { Button, Card, Modal, Table, Tabs, Tag, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { FaEye } from 'react-icons/fa';
import { LuFiles } from 'react-icons/lu';
import { useNavigate } from 'react-router-dom';
import MediaViewer from '../../../components/global/components/MediaViewer';
import RightSidebar from '../../../components/global/components/RightSidebar';
import { useLazyGetMediaByIdQuery } from '../../../slices/mediaSlice';
import { useGetHrmsUserByIdQuery } from '../../../slices/userApiSlice';
const { Title, Text } = Typography;

const GeneralDetailsRow = ({ name, Icon, value, color, className }) => {
  return (
    <div
      className={`flex items-center justify-between border-b pb-2 ${className}`}
    >
      <div className="flex items-center gap-2">
        <Icon className={`${color} text-xl`} />
        <span className="font-medium text-gray-600">{name}</span>
      </div>
      <span className="text-gray-800 font-medium">{value}</span>
    </div>
  );
};

const tabItems = [
  {
    label: `General`,
    key: 'general',
  },
  {
    label: `Transactions`,
    key: 'transactions',
  },
  {
    label: `Payrolls`,
    key: 'payrolls',
  },
];

const HrmsUserSidebar = ({ openSideBar, setOpenSideBar, recordId }) => {
  const [getMediaById] = useLazyGetMediaByIdQuery();
  const navigate = useNavigate();
  const { data: record } = useGetHrmsUserByIdQuery(
    { id: recordId },
    { skip: recordId === undefined }
  );
  const [transactions, setTransactions] = useState([]);
  const [selectedTab, setSelectedTab] = useState('general');
  const [showMedia, setShowMedia] = useState(false);
  const [media, setMedia] = useState([]);
  const [selectedMedia, setSelectedMedia] = useState({ data: '', type: '' });

  const sortByDate = (array) => {
    return array.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA - dateB; // For ascending (oldest first)
    });
  };

  useEffect(() => {
    let temp = [];
    if (record?.bonusData?.length > 0) {
      for (let i of record?.bonusData) {
        temp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          credit: true,
          type: 'Bonus',
        });
      }
    }
    if (record?.deductionData?.length > 0) {
      for (let i of record?.deductionData) {
        temp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          credit: false,
          type: 'Deduction',
        });
      }
    }
    if (record?.advanceData?.length > 0) {
      for (let i of record?.advanceData) {
        temp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          credit: true,
          type: 'Advance',
        });
      }
    }
    if (record?.adHocData?.length > 0) {
      for (let i of record?.adHocData) {
        temp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          credit: true,
          media: i?.media,
          type: 'Ad Hoc',
        });
      }
    }
    let sorted = sortByDate(temp);
    setTransactions(sorted);
  }, [record]);

  const columns = [
    {
      title: 'Date',
      key: 'date',
      render: (_, record) => <Tag color="orange">{record?.date}</Tag>,
    },
    {
      title: 'Credit/Debit',
      key: 'creditDebit',
      render: (_, record) => (
        <p
          className={`${record?.credit ? 'text-green-500' : 'text-red-500'}`}
        >{`${record?.credit ? '+' : '-'} ₹${record?.amount}`}</p>
      ),
    },
    {
      title: 'Type',
      key: 'type',
      render: (_, record) => <Tag color="orange">{record?.type}</Tag>,
    },
    {
      title: 'Media',
      key: 'media',
      render: (_, record) => (
        <>
          {record?.media?.length > 0 && (
            <div
              className="p-[6px] rounded-[5px] bg-blue-500 hover:bg-blue-400 cursor-pointer w-fit relative"
              onClick={async () => {
                setShowMedia(true);
                let temp = [];
                for (let i of record?.media) {
                  if (i?.data === undefined) {
                    let med = await getMediaById({ id: i });
                    temp?.push(med?.data?.media);
                  } else {
                    temp?.push(i);
                  }
                }
                //  setSelectedAdHoc(index);
                setMedia(temp);
              }}
            >
              <LuFiles className="text-lg text-white w-fit" />
              {record?.media?.length > 0 && (
                <span className="rounded-full py-[2px] px-[7px] text-[10px] text-white bg-red-500 absolute top-[-0.6rem] left-[1.2rem]">
                  {record?.media?.length}
                </span>
              )}
            </div>
          )}
        </>
      ),
    },
  ];

  const payrollColumns = [
    {
      title: 'Payroll',
      key: 'payrollRange',
      render: (_, record) => (
        <Tag color="orange">
          {record?.startDate !== undefined
            ? new Date(record?.startDate)?.toISOString()?.split('T')?.[0]
            : ''}{' '}
          -{' '}
          {record?.startDate !== undefined
            ? new Date(record?.endDate)?.toISOString()?.split('T')?.[0]
            : ''}
        </Tag>
      ),
    },
    {
      title: '',
      key: 'actions',
      render: (_, payroll) => (
        <Button
          type="primary"
          onClick={() => {
            navigate(`/hrms/payroll/view/${payroll?._id}/${record?._id}`);
          }}
        >
          View
        </Button>
      ),
    },
  ];

  const mediaColumns = [
    {
      title: 'Name',
      key: 'mediaName',
      render: (_, record) => <p>{record?.name}</p>,
    },
    {
      title: 'Type',
      key: 'mediaType',
      render: (_, record) => <Tag color="purple">{record?.type}</Tag>,
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <div className="flex items-center gap-2 justify-end">
          <FaEye
            onClick={() => {
              setSelectedMedia(record);
            }}
            className="text-2xl text-slate-300 hover:text-slate-400 cursor-pointer"
          />
        </div>
      ),
    },
  ];

  const closeMediaModal = () => {
    setShowMedia(false);
  };

  return (
    <>
      {selectedMedia?.data?.length !== 0 && openSideBar && (
        <MediaViewer media={selectedMedia} setMedia={setSelectedMedia} />
      )}
      <Modal
        title="AdHoc Media"
        //   onOk={handleUpdateDefaultLeads}
        onCancel={closeMediaModal}
        open={showMedia}
        width={800}
        footer={[
          <Button key="back" onClick={closeMediaModal}>
            Done
          </Button>,
        ]}
      >
        <div className="h-[40vh] overflow-y-scroll">
          <Table
            columns={mediaColumns}
            dataSource={media}
            rowKey={(_, index) => index}
            pagination={false}
            size="middle"
            scroll={{ x: true }}
            locale={{ emptyText: 'No Data added yet' }}
          />
        </div>
      </Modal>
      <RightSidebar
        openSideBar={openSideBar}
        setOpenSideBar={setOpenSideBar}
        title="Employee Details"
        scale={600}
      >
        <Tabs
          defaultActiveKey="workOrder"
          centered
          items={tabItems}
          onChange={(e) => {
            setSelectedTab(e);
          }}
        />
        {selectedTab === 'general' && (
          <>
            <div>
              <h4 className="my-2">General Details</h4>
              <div className="border rounded p-4">
                <GeneralDetailsRow
                  name={'Name'}
                  Icon={IdcardOutlined}
                  value={record?.name}
                  color={'text-indigo-500'}
                />
                <GeneralDetailsRow
                  name={'Email'}
                  Icon={IdcardOutlined}
                  value={record?.email}
                  color={'text-purple-500'}
                  className="mt-2"
                />
                <GeneralDetailsRow
                  name={'Gender'}
                  Icon={UserOutlined}
                  value={record?.gender}
                  color={'text-indigo-500'}
                  className="mt-2"
                />
                <GeneralDetailsRow
                  name={'Contact Number'}
                  Icon={PhoneOutlined}
                  value={record?.contactNumber}
                  color={'text-yellow-500'}
                  className="mt-2"
                />
                <GeneralDetailsRow
                  name={'Whatsapp Number'}
                  Icon={WhatsAppOutlined}
                  value={record?.whatsappNumber}
                  color={'text-green-500'}
                  className="mt-2"
                />
                <GeneralDetailsRow
                  name={'Working Hours'}
                  Icon={ClockCircleOutlined}
                  value={record?.workingHours}
                  color={'text-orange-500'}
                  className="mt-2"
                />
                <GeneralDetailsRow
                  name={'Fixed Salary'}
                  Icon={DollarOutlined}
                  value={record?.fixedSalary}
                  color={'text-lime-500'}
                  className="mt-2"
                />
              </div>
            </div>
            <div>
              <Card
                className="my-4"
                title={
                  <div className="flex items-center gap-2">
                    <BankOutlined className="text-primary" />
                    <Title level={5} className="!mb-0">
                      Bank Details
                    </Title>
                  </div>
                }
              >
                <div className="flex items-start px-6 py-2 border-b last:border-b-0 border-gray-100">
                  <Text className="w-48 text-gray-500 font-medium">
                    Bank Name
                  </Text>
                  <Text className="flex-1 text-gray-700 text-right">
                    {record?.bankName || '-'}
                  </Text>
                </div>
                <div className="flex items-start px-6 py-2 border-b last:border-b-0 border-gray-100">
                  <Text className="w-48 text-gray-500 font-medium">
                    Account Number
                  </Text>
                  <Text className="flex-1 text-gray-700 text-right">
                    {record?.accountNumber || '-'}
                  </Text>
                </div>
                <div className="flex items-start px-6 py-2 border-b last:border-b-0 border-gray-100">
                  <Text className="w-48 text-gray-500 font-medium">
                    IIFC Code
                  </Text>
                  <Text className="flex-1 text-gray-700 text-right">
                    {record?.iifcCode || '-'}
                  </Text>
                </div>
              </Card>
            </div>
          </>
        )}
        {selectedTab === 'transactions' && (
          <>
            <Table
              columns={columns}
              dataSource={transactions}
              rowKey={(_, index) => index}
              pagination={false}
              size="middle"
              scroll={{ x: true }}
              locale={{ emptyText: 'No Data added yet' }}
            />
          </>
        )}
        {selectedTab === 'payrolls' && (
          <>
            <Table
              columns={payrollColumns}
              dataSource={record?.payroll}
              rowKey={(_, index) => index}
              pagination={false}
              size="middle"
              scroll={{ x: true }}
              locale={{ emptyText: 'No Data added yet' }}
            />
          </>
        )}
      </RightSidebar>
    </>
  );
};

export default HrmsUserSidebar;
