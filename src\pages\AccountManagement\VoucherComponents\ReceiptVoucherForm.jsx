import { ArrowLeftOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, DatePicker, Form } from 'antd';
import dayjs from 'dayjs';
import { useContext, useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import Input from '../../../components/global/components/Input';
import SelectV2 from '../../../components/global/components/SelectV2';
import Textarea from '../../../components/global/components/Textarea';
import ProductFormatManager from '../../../components/ProductFormats/ProductFormatManager';
import StaticProductTable from '../../../components/ProductFormats/StaticProductTable';
import { renderFieldsBasedOnType } from '../../../helperFunction';
import usePrefixIds from '../../../hooks/usePrefixIds';
import {
  useCreateVoucherMutation,
  useEditVoucherMutation,
  useGetVoucherByIdQuery,
} from '../../../slices/AccountManagement/voucherApiSlice';
import { useGetDropdownsQuery } from '../../../slices/dropdownApiSlice';
import { useLazyQueryTemplateByIdQuery } from '../../../slices/dsahboardTemplateApiSlice';
import { Store } from '../../../store/Store';

const ReceiptVoucherForm = ({ props }) => {
  const { setOpenModal, editData, setEditData } = props;

  const [editVoucher] = useEditVoucherMutation();
  const { data: voucher } = useGetVoucherByIdQuery(
    { id: editData?._id },
    { skip: editData?._id === undefined }
  );
  const { defaults: { defaultParam } = {} } = useContext(Store);

  const isEditing = editData?._id !== undefined;

  const [createVoucher] = useCreateVoucherMutation();

  const [additionalFields, setAdditionalFields] = useState(null);
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();

  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
  });

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'receiptVoucherId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
    setIdData: setFormData,
  });

  const [items, setItems] = useState(
    defaultParam?.projectDefaults?.showProductFormatTable || isEditing
      ? []
      : [
          {
            key: Date.now() + Math.random(),
            itemId: '',
            productName: '',
            uom: '',
            hsn: '',
            quantity: '',
            rate: '',
            discount: '',
            amount: 0,
            cgst: '',
            sgst: '',
            cgstAmount: 0,
            sgstAmount: 0,
            igst: '',
            igstAmount: 0,
            totalAmount: 0,
            color: '#FFFFFF',
          },
        ]
  );

  const [charges, setCharges] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [chargesVisibility, setChargesVisibility] = useState({});
  const [displayFormat, setDisplayFormat] = useState(null);
  const { data: dropdownsData } = useGetDropdownsQuery();

  const changeHandler = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  useEffect(() => {
    const path = '/accountmanagement/voucher/receiptVoucher';
    getTemplates({ path });
  }, [getTemplates]);

  useEffect(() => {
    if (voucher?._id && voucher?.receiptVoucherData?.additionalFields) {
      setAdditionalFields(voucher.receiptVoucherData.additionalFields);
      setSelectedTemplate(voucher.receiptVoucherData.additionalFields);
    } else if (templatesData && Array.isArray(templatesData)) {
      const defaultTemplate =
        templatesData.find((t) => t?.name?.startsWith('Default')) ||
        templatesData?.[0];
      if (defaultTemplate) {
        setAdditionalFields(defaultTemplate);
        setSelectedTemplate(defaultTemplate);
      }
    }
  }, [
    voucher?._id,
    voucher?.receiptVoucherData?.additionalFields,
    templatesData,
  ]);

  const productFormatData = useMemo(() => {
    const rv = voucher?.receiptVoucherData || {};
    const pf = rv?.productTableFormat;
    const pfId = typeof pf === 'object' ? pf?._id : pf;
    return {
      productDetailsFromFormat: rv?.items || [],
      productChargesFromFormat: rv?.charges || {},
      productTableColumnHideStatus: rv?.columnVisibility || {},
      productTableChargesHideStatus: rv?.chargesVisibility || {},
      productTableFormat: pfId || null,
    };
  }, [voucher]);

  // Prefill form when editing
  useEffect(() => {
    if (voucher?._id) {
      let date =
        voucher?.date !== undefined
          ? new Date(voucher?.date)?.toISOString()?.split('T')?.[0]
          : '';
      setFormData({
        date: date,
        receiptVoucherId: voucher?.receiptVoucherId,
        voucherType: voucher?.voucherType,
        paymentType: voucher?.receiptVoucherData?.paymentType,
        from: voucher?.receiptVoucherData?.from || '',
        billTo: voucher?.receiptVoucherData?.billTo || '',
        remarks: voucher?.remarks || '',
        additionalFields: voucher?.receiptVoucherData?.additionalFields || null,
      });

      // Set other state
      if (
        voucher?.receiptVoucherData?.items &&
        voucher.receiptVoucherData.items.length > 0
      ) {
        setItems(voucher.receiptVoucherData.items);
      }
      if (voucher?.receiptVoucherData?.charges) {
        setCharges(voucher.receiptVoucherData.charges);
      }
      if (voucher?.receiptVoucherData?.columnVisibility) {
        setColumnVisibility(voucher.receiptVoucherData.columnVisibility);
      }
      if (voucher?.receiptVoucherData?.chargesVisibility) {
        setChargesVisibility(voucher.receiptVoucherData.chargesVisibility);
      }
      if (voucher?.receiptVoucherData?.productTableFormat) {
        setDisplayFormat(voucher.receiptVoucherData.productTableFormat);
      }
      // Set additional fields if they exist
      if (voucher?.receiptVoucherData?.additionalFields) {
        setAdditionalFields(voucher.receiptVoucherData.additionalFields);
        setSelectedTemplate(voucher.receiptVoucherData.additionalFields);
      }
    }
  }, [voucher]);

  const uomOptions = useMemo(() => {
    return (
      dropdownsData?.dropdowns
        ?.find((e) => e.name === 'uom')
        ?.values?.map((option) => ({
          label: option,
          value: option,
        })) || []
    );
  }, [dropdownsData]);

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }

    if (fieldValue === '+') {
      setDropdownIdx(idx);
      setTemplateDropDownModal(true);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field) => {
          if (field?.fieldName === fieldName) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );
      setAdditionalFields((prev) => ({
        ...prev,
        templateData: updatedAdditionalFields,
      }));
    }
  };

  const handleSubmit = async () => {
    const transformedItems = items.map((item) => ({ ...item }));
    const obj = {
      ...formData,
      date: formData.date
        ? new Date(formData.date).toISOString()
        : new Date().toISOString(),
      items: transformedItems,
      idData: idCompData?.dataToReturn,
      charges,
      columnVisibility,
      chargesVisibility,
      productTableFormat: displayFormat?._id || displayFormat || null,
      additionalFields: additionalFields || formData?.additionalFields || null,
    };

    try {
      const res = editData?._id
        ? await editVoucher({ data: { updateData: obj, id: editData._id } })
        : await createVoucher({
            data: { ...obj, voucherType: 'receiptVoucher' },
          });

      if (!res?.error) {
        setOpenModal(false);
        setFormData({
          date: new Date().toISOString().split('T')[0],
        });
        toast.success(
          `Receipt Voucher ${editData?._id ? 'Updated' : 'Created'} successfully`
        );
      } else {
        toast.error(
          'Faced an error while creating voucher, please reload and try again.'
        );
      }
    } catch (error) {
      toast.error(
        'Faced an error while creating voucher, please reload and try again.'
      );
    }
  };

  return (
    <>
      <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
        {/* Header */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                setOpenModal(false);
                setEditData({});
              }}
              type="text"
              size="small"
              className="hover:bg-gray-200"
            />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-0">
                {isEditing ? 'Edit' : 'Create'} Receipt Voucher
              </h2>
              <p className="text-sm text-gray-600 mb-0">
                {isEditing
                  ? 'Update receipt voucher information'
                  : 'Create a new receipt voucher'}
              </p>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <Form layout="vertical" onFinish={handleSubmit}>
          <div className="p-4 space-y-4">
            {/* Basic Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Receipt Voucher ID
                  </label>
                  {isEditing ? (
                    <Input
                      disabled
                      className="text-sm bg-gray-50"
                      value={formData?.receiptVoucherId}
                    />
                  ) : (
                    <IdGenComp {...idCompData} />
                  )}
                </div>

                <div className="space-y-1">
                  <label className="block mb-1 text-sm text-gray-500 font-medium">
                    Choose Template
                  </label>
                  <SelectV2
                    options={templatesData?.map((template) => ({
                      value: template?._id,
                      name: template?.name,
                    }))}
                    value={selectedTemplate?._id}
                    onChange={(e) => {
                      const template = templatesData.find(
                        (t) => t._id === e.target.value
                      );
                      if (selectedTemplate?._id === e.target.value) {
                        return;
                      }
                      setAdditionalFields(template);
                      setSelectedTemplate(template);
                      if (selectedTemplate?.idIndex === template.idIndex) {
                        return;
                      }
                    }}
                  />
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Date
                  </label>
                  <DatePicker
                    format={'DD-MM-YYYY'}
                    className="text-sm placeholder:text-gray-400 w-full"
                    size="middle"
                    name="date"
                    value={formData?.date ? dayjs(formData.date) : null}
                    onChange={(e) =>
                      changeHandler({
                        target: {
                          name: 'date',
                          value: e?.format('YYYY-MM-DD'),
                        },
                      })
                    }
                  />
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Payment Type
                  </label>
                  <Input
                    placeholder="Enter payment type"
                    className="text-sm"
                    name="paymentType"
                    value={formData.paymentType}
                    onChange={(e) => changeHandler(e)}
                  />
                </div>
              </div>
            </div>

            {/* Receipt Details */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Receipt Details
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    From
                  </label>
                  <Input
                    placeholder="Enter sender"
                    className="text-sm"
                    name="from"
                    value={formData.from}
                    onChange={(e) => changeHandler(e)}
                  />
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Bill To
                  </label>
                  <Input
                    placeholder="Enter receiver"
                    className="text-sm"
                    name="billTo"
                    value={formData.billTo}
                    onChange={(e) => changeHandler(e)}
                  />
                </div>
              </div>
            </div>
            {/* Product Table */}
            {(() => {
              const useFormatTable = isEditing
                ? !!voucher?.receiptVoucherData?.productTableFormat
                : defaultParam?.projectDefaults?.showProductFormatTable;

              return (
                <div className="bg-white border border-gray-200 rounded-lg p-3">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    {useFormatTable ? 'Product Format Table' : 'Product Table'}
                  </h3>
                  {useFormatTable ? (
                    <ProductFormatManager
                      input={items}
                      setInput={setItems}
                      charges={charges}
                      setCharges={setCharges}
                      columnVisibility={columnVisibility}
                      setColumnVisibility={setColumnVisibility}
                      chargesVisibility={chargesVisibility}
                      setChargesVisibility={setChargesVisibility}
                      displayFormat={displayFormat}
                      setDisplayFormat={setDisplayFormat}
                      isEdit={isEditing}
                      isCopy={false}
                      data={productFormatData}
                    />
                  ) : (
                    <StaticProductTable
                      input={items}
                      setInput={setItems}
                      charges={charges}
                      setCharges={setCharges}
                      uomOptions={uomOptions}
                    />
                  )}
                </div>
              );
            })()}

            {/* Template Details */}
            {additionalFields?.templateData?.length > 0 && (
              <div className="bg-white border border-gray-200 rounded-lg p-3">
                <h3 className="text-sm font-medium text-gray-700 mb-3">
                  Template Details
                </h3>
                <section className="w-full">
                  {renderFieldsBasedOnType(
                    additionalFields,
                    handleInputChange,
                    templateDropDownModal,
                    setTemplateDropDownModal,
                    setAdditionalFields,
                    newOptionStatus,
                    setNewOptionStatus,
                    dropdownIdx,
                    setDropdownIdx
                  )}
                </section>
              </div>
            )}

            {/* Remarks */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Remarks
              </h3>
              <Textarea
                name="remarks"
                value={formData.remarks}
                onChange={(e) => changeHandler(e)}
                rows={3}
                placeholder="Enter additional remarks or comments"
                className="text-sm resize-none"
              />
            </div>
          </div>

          {/* Footer Actions */}
          <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
            <div className="flex items-center justify-end">
              <Button
                htmlType="submit"
                type="primary"
                size="small"
                className="text-sm px-4 py-1 h-8"
              >
                {isEditing ? 'Update' : 'Save'} Receipt Voucher
              </Button>
            </div>
          </div>
        </Form>
      </div>
    </>
  );
};

export default ReceiptVoucherForm;
