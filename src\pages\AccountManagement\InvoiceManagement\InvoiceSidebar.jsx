import {
  BarcodeOutlined,
  HomeOutlined,
  IdcardOutlined,
  MailOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import { Button, Descriptions, Spin, Table, Tag, Typography } from 'antd';
import { Edit, Trash } from 'lucide-react';
import { useState } from 'react';
import { PiFilePdfDuotone } from 'react-icons/pi';
import { toast } from 'react-toastify';
import RightSidebar from '../../../components/global/components/RightSidebar';
import SelectV2 from '../../../components/global/components/SelectV2';
import PoGlobalSidebar from '../../../components/po-dashboard/PoGlobalSidebar';
import SoGlobalSidebar from '../../../components/salesOrder/SoGlobalSidebar';
import { handlePdf } from '../../../helperFunction';
import {
  useDeleteInvoiceMutation,
  useGetInvoiceByIdQuery,
  useUpdateInvoiceStatusMutation,
} from '../../../slices/AccountManagement/invoicesApiSlice';
import { useLazyGetPdfQuery } from '../../../slices/pdfApiSlice';
import StatusTimeline from './StatusTimeline';

const { Title, Text } = Typography;

const InvoiceSidebar = ({ id, openSidebar, setOpenSidebar, onEdit, type }) => {
  const { data: invoice, isLoading } = useGetInvoiceByIdQuery(
    { id },
    { skip: !id }
  );
  const [updateStatus] = useUpdateInvoiceStatusMutation();
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const [deleteInvoice] = useDeleteInvoiceMutation();
  const [openSalesOrderSidebar, setOpenSalesOrderSidebar] = useState(false);
  const statusOptions = ['draft', 'sent', 'paid', 'overdue', 'cancelled'];
  const [selectedPo, setSelectedPo] = useState(null);
  const [openSideBar, setOpenSideBar] = useState(false);

  const handleStatusChange = async (invoiceId, newStatus) => {
    try {
      await updateStatus({ id: invoiceId, status: newStatus });
      toast.success('Invoice status updated successfully');
    } catch (error) {
      toast.error('Failed to update invoice status');
    }
  };

  const handlePdfDownload = () => {
    handlePdf(getPdf, invoice?._id, 'invoice');
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(id);
    }
    setOpenSidebar(false);
  };

  const handleDeleteInvoice = async () => {
    try {
      await deleteInvoice({ id }).unwrap();
      toast.success('Invoice deleted successfully');
      setOpenSidebar(false);
    } catch (error) {
      toast.error('Failed to delete invoice');
    }
  };

  const ActionButton = ({
    onClick,
    disabled,
    icon,
    tooltip,
    color = 'text-gray-700',
  }) => {
    const Icon = icon;

    return (
      <button
        onClick={onClick}
        disabled={disabled}
        className={`p-2 rounded-full transition-all ${
          disabled
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:bg-gray-100 active:bg-gray-200'
        } group relative`}
      >
        <Icon className={`w-5 h-5 ${color}`} />
        {tooltip && (
          <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap z-10">
            {tooltip}
          </span>
        )}
      </button>
    );
  };

  const LoadingSpinner = () => (
    <div className="w-5 h-5 rounded-full border-2 border-gray-300 border-t-blue-500 animate-spin" />
  );

  if (isLoading) {
    return (
      <RightSidebar
        openSideBar={openSidebar}
        setOpenSideBar={setOpenSidebar}
        title="Invoice Details"
      >
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </RightSidebar>
    );
  }

  if (!invoice) {
    return (
      <RightSidebar
        openSideBar={openSidebar}
        setOpenSideBar={setOpenSidebar}
        title="Invoice Details"
      >
        <div className="text-center text-gray-500 mt-8">Invoice not found</div>
      </RightSidebar>
    );
  }

  return (
    <RightSidebar
      openSideBar={openSidebar}
      setOpenSideBar={setOpenSidebar}
      title={`${type === 'purchase' ? 'Purchase' : 'Sales'} Invoice Details`}
    >
      {openSalesOrderSidebar && (
        <RightSidebar
          openSideBar={openSalesOrderSidebar}
          setOpenSideBar={setOpenSalesOrderSidebar}
          title="Sales Order Details"
        >
          <SoGlobalSidebar id={invoice.salesOrderRef?._id} fromKanban={false} />
        </RightSidebar>
      )}
      <div className="space-y-4">
        {invoice.salesOrderRef && (
          <button onClick={() => setOpenSalesOrderSidebar(true)}>
            Open SO SIDEBAR
          </button>
        )}
        <div className="flex justify-end gap-2 mb-4">
          <ActionButton icon={Edit} tooltip="Edit" onClick={handleEdit} />
          <ActionButton
            icon={Trash}
            tooltip="Delete"
            color="text-red-500"
            onClick={handleDeleteInvoice}
          />
          <ActionButton
            icon={isFetchingPdf ? LoadingSpinner : PiFilePdfDuotone}
            tooltip="Download PDF"
            color="text-yellow-600"
            onClick={handlePdfDownload}
            disabled={isFetchingPdf}
          />
        </div>

        <Descriptions column={1} size="small" bordered>
          <Descriptions.Item label="Invoice ID">
            <Text strong>
              {invoice.salesInvoiceId || invoice.purchaseInvoiceId || 'N/A'}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Type">
            <Tag color={invoice.type === 'purchase' ? 'red' : 'green'}>
              {invoice.type?.toUpperCase() || 'SALES'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Creation Type">
            <Tag color={invoice.creationType === 'manual' ? 'blue' : 'orange'}>
              {invoice.creationType?.toUpperCase() || 'MANUAL'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Status">
            <SelectV2
              value={invoice?.status}
              onChange={(e) => handleStatusChange(invoice._id, e.target.value)}
              size="small"
              menuPosition="fixed"
              className="w-full max-w-2"
              options={statusOptions?.map((el) => ({
                label: el.charAt(0).toUpperCase() + el.slice(1),
                value: el,
              }))}
            />
          </Descriptions.Item>
          <Descriptions.Item label="Amount">
            <Text strong className="text-lg">
              ₹
              {(
                invoice.totalAmount ||
                invoice.salesOrderRef?.charges?.total ||
                invoice.purchaseOrderRef?.charges?.total ||
                0
              ).toLocaleString()}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Invoice Date">
            {new Date(invoice.invoiceDate).toLocaleDateString()}
          </Descriptions.Item>
          {invoice.salesOrderRef && (
            <>
              <Descriptions.Item label="Sales Order ID">
                <Text strong>
                  {invoice.salesOrderRef?.salesOrderID || 'N/A'}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Delivery Date">
                {invoice.salesOrderRef?.deliveryDate
                  ? new Date(
                      invoice.salesOrderRef.deliveryDate
                    ).toLocaleDateString()
                  : 'N/A'}
              </Descriptions.Item>
            </>
          )}
          {invoice.purchaseOrderRef && (
            <>
              <Descriptions.Item label="Purchase Order ID">
                <Text strong>{invoice.purchaseOrderRef?.poID || 'N/A'}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Expected Delivery">
                {invoice.purchaseOrderRef?.deliveryDate
                  ? new Date(
                      invoice.purchaseOrderRef.deliveryDate
                    ).toLocaleDateString()
                  : 'N/A'}
              </Descriptions.Item>
            </>
          )}
        </Descriptions>

        {/* Customer/Vendor Information */}
        {invoice.type === 'sales' &&
          (invoice.salesOrderRef?.CustomerData || invoice.customer) && (
            <div>
              <p className="font-medium">Customer Information</p>
              <div className="grid gap-4 border rounded-lg p-4 mt-2">
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <IdcardOutlined className="text-blue-500 text-xl" />
                    <span className="font-medium">Name</span>
                  </Text>
                  <Tag color="blue" className="font-medium">
                    {invoice.customer?.name ||
                      invoice.salesOrderRef?.customer ||
                      'N/A'}
                  </Tag>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <IdcardOutlined className="text-green-500 text-xl" />
                    <span className="font-medium">Company</span>
                  </Text>
                  <span className="font-medium">
                    {invoice.customer?.company_name ||
                      invoice?.salesOrderRef?.CustomerData?.company_name ||
                      'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <MailOutlined className="text-green-500 text-xl" />
                    <span className="font-medium">Email</span>
                  </Text>
                  <span className="font-medium">
                    {invoice.customer?.unique_id?.[0] ||
                      invoice.salesOrderRef?.CustomerData?.unique_id?.[0] ||
                      'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <PhoneOutlined className="text-purple-500 text-xl" />
                    <span className="font-medium">Contact</span>
                  </Text>
                  <span className="font-medium">
                    {invoice.customer?.phone_no?.[0] ||
                      invoice.salesOrderRef?.CustomerData?.phone_no?.[0] ||
                      'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <HomeOutlined className="text-orange-500 text-xl" />
                    <span className="font-medium">Billing Address</span>
                  </Text>
                  <span className="font-medium">
                    {invoice.customer?.billingAddress?.[0] ||
                      invoice.salesOrderRef?.billingAddress ||
                      'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <HomeOutlined className="text-blue-500 text-xl" />
                    <span className="font-medium">Delivery Address</span>
                  </Text>
                  <span className="font-medium">
                    {invoice.customer?.deliveryAddress?.[0] ||
                      invoice.salesOrderRef?.deliveryAddress ||
                      'N/A'}
                  </span>
                </div>
                {(invoice.customer?.gstNumber?.[0] ||
                  invoice.salesOrderRef?.CustomerData?.gstNumber?.[0]) && (
                  <div className="flex items-center justify-between">
                    <Text className="flex items-center gap-3 text-gray-600">
                      <BarcodeOutlined className="text-pink-500 text-xl" />
                      <span className="font-medium">GSTIN</span>
                    </Text>
                    <span className="font-medium">
                      {invoice.customer?.gstNumber?.[0] ||
                        invoice.salesOrderRef?.CustomerData?.gstNumber?.[0]}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

        {invoice.type === 'purchase' &&
          (invoice.purchaseOrderRef?.vendor || invoice.vendor) && (
            <div>
              <p className="font-medium">Vendor Information</p>
              <div className="grid gap-4 border rounded-lg p-4 mt-2">
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <IdcardOutlined className="text-blue-500 text-xl" />
                    <span className="font-medium">Name</span>
                  </Text>
                  <Tag color="red" className="font-medium">
                    {invoice.purchaseOrderRef?.vendor?.name ||
                      invoice.vendor?.name ||
                      'N/A'}
                  </Tag>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <IdcardOutlined className="text-green-500 text-xl" />
                    <span className="font-medium">Company</span>
                  </Text>
                  <span className="font-medium">
                    {invoice.purchaseOrderRef?.vendor?.company_name ||
                      invoice.vendor?.company_name ||
                      'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <MailOutlined className="text-green-500 text-xl" />
                    <span className="font-medium">Email</span>
                  </Text>
                  <span className="font-medium">
                    {invoice.purchaseOrderRef?.vendor?.email ||
                      invoice.vendor?.email ||
                      'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <PhoneOutlined className="text-purple-500 text-xl" />
                    <span className="font-medium">Contact</span>
                  </Text>
                  <span className="font-medium">
                    {invoice.purchaseOrderRef?.vendor?.phone ||
                      invoice.vendor?.contact?.[0] ||
                      'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <HomeOutlined className="text-orange-500 text-xl" />
                    <span className="font-medium">Address</span>
                  </Text>
                  <span className="font-medium">
                    {invoice.purchaseOrderRef?.vendor?.address ||
                      invoice.vendor?.address ||
                      'N/A'}
                  </span>
                </div>
                {(invoice.purchaseOrderRef?.vendor?.gstNumber ||
                  invoice.vendor?.gstNumber) && (
                  <div className="flex items-center justify-between">
                    <Text className="flex items-center gap-3 text-gray-600">
                      <BarcodeOutlined className="text-pink-500 text-xl" />
                      <span className="font-medium">GSTIN</span>
                    </Text>
                    <span className="font-medium">
                      {invoice.purchaseOrderRef?.vendor?.gstNumber ||
                        invoice.vendor?.gstNumber}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

        {!invoice.salesOrderRef &&
          !invoice.purchaseOrderRef &&
          invoice.productDetails?.products?.length > 0 && (
            <div>
              <Title level={5}>Products</Title>
              <Table
                dataSource={(
                  invoice.productDetails?.products || invoice.productDetails
                )?.map((item, index) => ({
                  key: index,
                  ...item,
                }))}
                columns={[
                  {
                    title: 'Item',
                    dataIndex: 'productName',
                    key: 'item',
                    render: (_, record) => (
                      <div>
                        <Text strong className="block text-xs">
                          {record.productName || record.itemName || record.name}
                        </Text>
                        {record.hsn && (
                          <Text className="text-gray-500 text-xs">
                            HSN: {record.hsn}
                          </Text>
                        )}
                      </div>
                    ),
                  },
                  {
                    title: 'UOM',
                    dataIndex: 'uom',
                    key: 'uom',
                    width: 60,
                    render: (uom) => (
                      <Text className="text-xs">{uom || 'N/A'}</Text>
                    ),
                  },
                  {
                    title: 'Qty',
                    dataIndex: 'quantity',
                    key: 'quantity',
                    width: 50,
                    render: (qty) => (
                      <Text className="text-xs font-medium">{qty}</Text>
                    ),
                  },
                  {
                    title: 'Rate',
                    dataIndex: 'rate',
                    key: 'rate',
                    width: 70,
                    render: (rate) =>
                      rate ? <Text className="text-xs">₹{rate}</Text> : '-',
                  },
                  {
                    title: 'Disc%',
                    dataIndex: 'discount',
                    key: 'discount',
                    width: 50,
                    render: (discount) =>
                      discount ? (
                        <Text className="text-xs">{discount}%</Text>
                      ) : (
                        '-'
                      ),
                  },
                  {
                    title: 'Amount',
                    dataIndex: 'amount',
                    key: 'amount',
                    width: 70,
                    render: (amount) => (
                      <Text className="text-xs">₹{amount || 0}</Text>
                    ),
                  },
                  {
                    title: 'CGST',
                    dataIndex: 'cgst',
                    key: 'cgst',
                    width: 50,
                    render: (cgst, record) => (
                      <div className="text-xs">
                        {cgst ? `${cgst}%` : '-'}
                        {record.cgstAmount && (
                          <div className="text-gray-500">
                            ₹{record.cgstAmount}
                          </div>
                        )}
                      </div>
                    ),
                  },
                  {
                    title: 'SGST',
                    dataIndex: 'sgst',
                    key: 'sgst',
                    width: 50,
                    render: (sgst, record) => (
                      <div className="text-xs">
                        {sgst ? `${sgst}%` : '-'}
                        {record.sgstAmount && (
                          <div className="text-gray-500">
                            ₹{record.sgstAmount}
                          </div>
                        )}
                      </div>
                    ),
                  },
                  {
                    title: 'IGST',
                    dataIndex: 'igst',
                    key: 'igst',
                    width: 50,
                    render: (igst, record) => (
                      <div className="text-xs">
                        {igst ? `${igst}%` : ''}
                        {record.igstAmount && record.igstAmount > 0 && (
                          <div className="text-gray-500">
                            ₹{record.igstAmount}
                          </div>
                        )}
                      </div>
                    ),
                  },
                  {
                    title: 'Total',
                    dataIndex: 'totalAmount',
                    key: 'totalAmount',
                    width: 80,
                    render: (totalAmount) => (
                      <Text className="text-xs font-medium text-green-600">
                        ₹{totalAmount || 0}
                      </Text>
                    ),
                  },
                ]}
                size="small"
                pagination={false}
                className="border rounded"
                scroll={{ x: 600 }}
              />
              {/* Charges Section */}
              {invoice.productDetails?.charges && (
                <div className="mt-4">
                  <Title level={5}>Additional Charges</Title>
                  <div className="bg-gray-50 p-3 rounded border">
                    {Object.entries(invoice.productDetails.charges).map(
                      ([key, value]) => (
                        <div
                          key={key}
                          className="flex justify-between items-center py-1"
                        >
                          <Text className="text-sm capitalize">
                            {key.replace(/([A-Z])/g, ' $1')}
                          </Text>
                          <Text className="text-sm font-medium">₹{value}</Text>
                        </div>
                      )
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        <div>
          {invoice?.purchaseOrderRef && (
            <Button
              onClick={() => {
                setSelectedPo(invoice?.purchaseOrderRef?._id),
                  setOpenSideBar(true);
              }}
            >
              View Purchase Order
            </Button>
          )}
        </div>
        <RightSidebar
          openSideBar={openSideBar}
          setOpenSideBar={setOpenSideBar}
          title="Purchase Order"
        >
          <PoGlobalSidebar
            poId={selectedPo}
            fromKanban={false}
            onClose={() => {}}
          />
        </RightSidebar>

        {invoice.statusTimeline && invoice.statusTimeline.length > 0 && (
          <div>
            <StatusTimeline statusTimeline={invoice.statusTimeline} />
          </div>
        )}
      </div>
    </RightSidebar>
  );
};

export default InvoiceSidebar;
