import { PuzzlePieceIcon } from '@heroicons/react/24/solid';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { getLocalDateTime } from '../../helperFunction';
import {
  useCreateLocationMutation,
  useGetAllLocationsQuery,
} from '../../slices/locationApiSlice';
import Button from '../global/components/Button';
import { InfoTooltip } from '../global/components/InfoTooltip';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import NewInput from '../global/components/NewInput';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import AccountManagement from './AccountManagement';
import CRMDefaults from './CRMDefaults';
import KanbanDefaults from './KanbanDefaults';
import ProductFormatTableDefaults from './ProductFormatTableDefaults';
import ProductMasterDefaults from './ProductMasterDefaults';
import SalesDefaults from './SalesDefaults';
import VariantDefaults from './VariantDefaults';

const ProjectDefaults = ({
  defaults,
  setDefaults,
  allDashboard,
  defaultParam,
}) => {
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false);
  const [inputs, setInputs] = useState({
    locationName: '',
    locationtype: 'N/A',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [enableMinimizedView, setEnableMinimizedView] = useState(
    defaults?.projectDefaults?.enableMinimizedView || false
  );
  const [enableImageInQuotation, setEnableImageInQuotation] = useState(
    defaults?.projectDefaults?.enableImageInQuotation || false
  );

  const [enableImageInSalesOrder, setEnableImageInSalesOrder] = useState(
    defaults?.projectDefaults?.enableImageInSalesOrder || false
  );
  const [createLocation] = useCreateLocationMutation();
  const [editMode, setEditMode] = useState(false);

  const { data: allLocations = [] } = useGetAllLocationsQuery();
  const locationsType = [
    'Scrap Location',
    'Rework Location',
    'Quality Check Location',
  ];

  const handleInputChange = (e) => {
    setInputs((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const renderInfoTooltip = (content) => {
    return (
      <InfoTooltip
        position="top"
        width="200px"
        id="downtimeType"
        isHtml={true}
        content={content}
      />
    );
  };

  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      createLocation(inputs).then(() => {
        setIsLoading(false);
        setIsLocationModalOpen(false);
        setInputs({
          locationName: '',
          locationtype: 'N/A',
        });
      });
    } catch (error) {
      console.log(error); //eslint-disable-line
      toast.error(error?.response?.data?.message || error?.message, {
        position: 'top-right',
        toastId: error?.response?.data?.message || error?.message,
      });
    }
  };
  const addExcelLabel = () => {
    const len = defaults?.projectDefaults?.excelSheetLabel.length;
    setDefaults((prev) => ({
      ...prev,
      projectDefaults: {
        ...prev.projectDefaults,
        excelSheetLabel: [
          ...prev.projectDefaults.excelSheetLabel,
          { labelName: `View Excel ${len + 1}`, label: '', idx: len },
        ],
      },
    }));
  };

  const handleExcelInput = (e, index) => {
    const { value } = e.target;
    setDefaults((prev) => {
      const updatedLabels = [...prev.projectDefaults.excelSheetLabel];
      updatedLabels[index] = {
        ...updatedLabels[index],
        label: value,
      };
      return {
        ...prev,
        projectDefaults: {
          ...prev.projectDefaults,
          excelSheetLabel: updatedLabels,
        },
      };
    });
  };

  const deleteExcelLabel = (idx) => {
    setDefaults((prev) => {
      const updatedLabels = prev.projectDefaults.excelSheetLabel.filter(
        (_, index) => index !== idx
      );
      return {
        ...prev,
        projectDefaults: {
          ...prev.projectDefaults,
          excelSheetLabel: updatedLabels,
        },
      };
    });
  };

  return (
    <div className="h-full  bg-white">
      <h3 className="text-gray-subHeading">Project Defaults:</h3>
      <div className="mt-4 grid grid-cols-1 md:grid-cols-4 lg:grid-cols-5 gap-4">
        <div className="w-full">
          <h5 className="mb-2 text-gray-label">Project Label:</h5>
          <Input
            value={defaults?.projectDefaults?.projectIdentifier}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  projectIdentifier: e.target.value,
                },
              }));
            }}
            placeholder="Enter Project Label"
          />
        </div>

        <div className="w-full">
          <h5 className="mb-2 text-gray-label">Input Screen Label:</h5>
          <Input
            value={defaults?.projectDefaults?.inputScreenLabel}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  inputScreenLabel: e.target.value,
                },
              }));
            }}
            placeholder="Enter Input Screen Label"
          />
        </div>

        <div className="w-full">
          <h5 className="mb-2 text-gray-label">Model Label:</h5>
          <Input
            value={defaults?.projectDefaults?.modelLabel}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  modelLabel: e.target.value,
                },
              }));
            }}
            placeholder="Enter Model Label"
          />
        </div>

        {defaults?.projectDefaults?.excelSheetLabel.map((val, idx) => (
          <div key={idx} className="w-full">
            {editMode ? (
              <input
                className="mb-2 text-gray-label outline-none w-full"
                onChange={(e) => {
                  let temp = defaults.projectDefaults?.excelSheetLabel.map(
                    (value, index) => {
                      if (index === idx) {
                        return { ...value, labelName: e.target.value };
                      } else {
                        return value;
                      }
                    }
                  );
                  setDefaults((prev) => ({
                    ...prev,
                    projectDefaults: {
                      ...prev.projectDefaults,
                      excelSheetLabel: temp,
                    },
                  }));
                }}
                onBlur={() => {
                  setEditMode(false);
                }}
                value={val.labelName || `View Excel ${idx + 1}`}
              />
            ) : (
              <div className="flex items-center mb-2">
                <h5
                  className="text-gray-label cursor-pointer"
                  onDoubleClick={() => setEditMode(true)}
                >
                  {val.labelName || `View Excel ${idx + 1}`}
                </h5>
                <button
                  className="rounded-full w-8 ml-2"
                  onClick={() => deleteExcelLabel(idx)}
                >
                  <span role="img" aria-label="Delete">
                    ❌
                  </span>
                </button>
              </div>
            )}
            <Input
              value={val.label}
              onChange={(e) => handleExcelInput(e, idx)}
              placeholder="Enter Input Screen Label"
            />
          </div>
        ))}

        <div className="w-full">
          <h5 className="mb-2 text-gray-label ml-1">Add</h5>
          <Button className="rounded-full" size="sm" onClick={addExcelLabel}>
            +
          </Button>
        </div>
      </div>
      <AccountManagement defaults={defaults} setDefaults={setDefaults} />
      <VariantDefaults defaults={defaults} setDefaults={setDefaults} />
      <ProductMasterDefaults defaults={defaults} setDefaults={setDefaults} />
      <ProductFormatTableDefaults
        defaults={defaults}
        setDefaults={setDefaults}
      />
      {/* Kanban Defaults  */}
      <KanbanDefaults defaults={defaults} setDefaults={setDefaults} />
      <div className="flex items-center gap-2 border-b-2 border-gray-300/70 pb-2">
        <h3 className="text-gray-subHeading">RFID Settings:</h3>
        <input
          type="checkbox"
          className="w-4 h-4"
          checked={defaults?.projectDefaults?.hideRFIDInStockout}
          onChange={(e) => {
            setDefaults((prev) => ({
              ...prev,
              projectDefaults: {
                ...prev.projectDefaults,
                hideRFIDInStockout: e.target.checked,
              },
            }));
          }}
        />
        <p className="ml-2 text-sm font-medium text-gray-700">
          Hide RFID Authorization Popup
        </p>
      </div>
      <div className="flex flex-col gap-2 border-b-2 border-gray-300/70 pb-2">
        <h3 className="text-gray-subHeading">Voucher Settings:</h3>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={
              defaults?.projectDefaults?.createStockOutRequestFromSalesVoucher
            }
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  directStockOutFromSalesVoucher: false,
                  createStockOutRequestFromSalesVoucher: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Create Stock Out Request From Sales Voucher
          </p>
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.directStockOutFromSalesVoucher}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  createStockOutRequestFromSalesVoucher: false,
                  directStockOutFromSalesVoucher: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Direct Stock Out From Sales Voucher
          </p>
        </div>
      </div>
      <CRMDefaults defaults={defaults} setDefaults={setDefaults} />
      <SalesDefaults defaults={defaults} setDefaults={setDefaults} />
      <div className="my-6 flex-col md:flex-row items-center gap-8 px-2 ">
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.pendingStockIn || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  pendingStockIn: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Require Inspection before Stock In
          </p>
          {renderInfoTooltip(
            'A Inspection is Required Before Stock In of Any Items'
          )}
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.dashboardViewCustomDep || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  dashboardViewCustomDep: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Enable Dashboard View in Custom Departments
          </p>
          {renderInfoTooltip('Enable Dashboard View in Custom Departments')}
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={
              defaults?.projectDefaults?.enableAutoStockOutChecked || false
            }
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  enableAutoStockOutChecked: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Enable Auto Stock Out Already Checked
          </p>
          {renderInfoTooltip(
            ' When this checkbox is enabled, the stock for finished goods will automatically  check auto stock out while stock in.'
          )}
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.enablePartStoreEdit || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  enablePartStoreEdit: e.target.checked,
                  enableStoreAdditionThroughMasterAndStockIn: false,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Enable store addition through master only
          </p>
          {renderInfoTooltip(
            'Enabling this checkbox will allow users to add stores only from the master - item.'
          )}
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={
              defaults?.projectDefaults
                ?.enableStoreAdditionThroughMasterAndStockIn || false
            }
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  enableStoreAdditionThroughMasterAndStockIn: e.target.checked,
                  enablePartStoreEdit: false,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Enable store addition through master and stock in
          </p>
          {renderInfoTooltip(
            'When this checkbox is enabled, all items will be displayed during stock-in operations, and stores can be assigned in parallel.'
          )}
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.showAdditionalUOM || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  showAdditionalUOM: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Enable Additional UOMS in PDF
          </p>
          {renderInfoTooltip(
            'This feature allows users to add additional units of measurement in PDF reports and other related documents.'
          )}
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.allowCustomKanbanId}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  allowCustomKanbanId: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Enable custom task ID in kanban
          </p>
          {renderInfoTooltip(
            'This feature allows users to assign a custom ID while creating Kanban tiles.'
          )}
        </div>
        {defaults?.projectDefaults?.allowCustomKanbanId && (
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              className="w-4 h-4"
              checked={defaults?.projectDefaults?.projectBasedCustomTaskId}
              onChange={(e) => {
                setDefaults((prev) => ({
                  ...prev,
                  projectDefaults: {
                    ...prev.projectDefaults,
                    projectBasedCustomTaskId: e.target.checked,
                  },
                }));
              }}
            />
            <p className="ml-2 text-sm font-medium text-gray-700">
              Enable Project Input based custom task ID in kanban
            </p>
            {renderInfoTooltip(
              `This feature enables users to create specific task IDs that include the project name and the month's initial letter. To activate this option, the "Enable Custom Task ID in Kanban" checkbox must also be enabled.`
            )}
          </div>
        )}
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={enableMinimizedView}
            onChange={(e) => {
              setEnableMinimizedView((prev) => !prev);
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  enableMinimizedView: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Enable minimized view for employees
          </p>
          {renderInfoTooltip(
            `This feature allows users to minimize Kanban columns and employee can see column name but cannot access.`
          )}
        </div>

        <div className="flex items-center gap-2">
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="w-4 h-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              checked={enableImageInQuotation}
              onChange={(e) => {
                setEnableImageInQuotation((prev) => !prev);
                setDefaults((prev) => ({
                  ...prev,
                  projectDefaults: {
                    ...prev.projectDefaults,
                    enableImageInQuotation: e.target.checked,
                  },
                }));
              }}
              aria-label="Enable image in Quotation"
            />
          </label>

          <p className="ml-2 text-sm font-medium text-gray-700">
            Enable Auto attachments in Quotation
          </p>

          <InfoTooltip
            position="top"
            width="200px"
            id="downtimeType"
            isHtml={true}
            content="Enable Auto fetch Part Media in quotation attachment"
          />
        </div>

        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={enableImageInSalesOrder}
            onChange={(e) => {
              setEnableImageInSalesOrder((prev) => !prev);
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  enableImageInSalesOrder: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            {' '}
            Enable Auto attachments in Sales Order
          </p>
          <InfoTooltip
            position="top"
            width="200px"
            id="downtimeType"
            isHtml={true}
            content="Enable Auto fetch Part Media in sales order attachment"
          />
        </div>

        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={
              defaults?.projectDefaults?.enableUserCheckinPlanner || false
            }
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  enableUserCheckinPlanner: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            {' '}
            Enable User checkin in job planner
          </p>
          <InfoTooltip
            position="top"
            width="200px"
            id="downtimeType"
            isHtml={true}
            content="Enable User checkin in job planner"
          />
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.isSingleProcessJob || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  isSingleProcessJob: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Single Process Production
          </p>
          <InfoTooltip
            position="top"
            width="200px"
            id="downtimeType"
            isHtml={true}
            content="Enable this to use single process job in production"
          />
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.jobOnlyViewInReport || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  jobOnlyViewInReport: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            View Job Info only in Report
          </p>
          <InfoTooltip
            position="top"
            width="200px"
            id="downtimeType"
            isHtml={true}
            content="Enable this to view only jobs information in report"
          />
        </div>
      </div>

      {/* adding homepage */}

      <div className="w-full grid grid-cols-1 md:grid-cols-4  gap-2">
        <div className="text-sm gap-2 w-full">
          <h4 className="w-full text-md mb-1 text-gray-subHeading my-auto">
            Select Homepage :
          </h4>
          <div className="pr-2 gap-x-7 flex items-center w-full ">
            <Select
              placeholder="Select Homepage"
              value={defaults?.homepage}
              onChange={(e) => {
                setDefaults((prev) => {
                  return { ...prev, homepage: e.target.value };
                });
                // setSelectedHome({ title: e.target.name, link: e.target.value });
              }}
              options={allDashboard?.map((name) => ({
                name: name.cname,
                value: name.cslug,
              }))}
            />
          </div>
        </div>
        <div className="text-sm gap-2 w-full ">
          <h4 className="w-full text-md mb-1 text-gray-subHeading my-auto">
            {`Select Primary Identifier (CU) : `}
          </h4>
          <div className="pr-2 gap-x-7 flex items-center w-full">
            <Select
              placeholder="Select Identifier"
              value={defaults?.cuIdentifier}
              onChange={(e) => {
                setDefaults((prev) => {
                  return { ...prev, cuIdentifier: e.target.value };
                });
                // setSelectedHome({ title: e.target.name, link: e.target.value });
              }}
              options={[
                {
                  name: `${defaultParam?.projectDefaults?.projectIdentifier}`,
                  value: 'Job',
                },
                {
                  name: `${defaultParam?.projectDefaults?.modelLabel}`,
                  value: 'Model',
                },
              ]}
            />
          </div>
        </div>
        <div className="text-sm gap-2 w-full">
          <h4 className="w-full text-md mb-1 text-gray-subHeading my-auto">
            Select Defult Sampling Method :
          </h4>
          <div className="  flex items-center w-full ">
            <Select
              placeholder="Sampling Method"
              value={defaults?.defaultSamplingMode}
              onChange={(e) => {
                setDefaults((prev) => {
                  return { ...prev, defaultSamplingMode: e.target.value };
                });
              }}
              options={['None', 'Single', 'Percentage', 'Sampling']?.map(
                (name) => ({
                  name: name,
                  value: name,
                })
              )}
            />
          </div>
        </div>

        <div className="text-sm gap-2 w-full">
          <h4 className="w-full text-md mb-1 text-gray-subHeading my-auto">
            Select Defult WorkOrder Type :
          </h4>
          <div className="flex items-center w-full ">
            <Select
              placeholder="WorkOrder Type"
              value={defaults?.projectDefaults?.defaultWoType}
              onChange={(e) => {
                setDefaults((prev) => ({
                  ...prev,
                  projectDefaults: {
                    ...prev.projectDefaults,
                    defaultWoType: e.target.value,
                  },
                }));
              }}
              options={['Machine', 'Assembly', 'All']?.map((name) => ({
                name: name,
                value: name.toLocaleLowerCase(),
              }))}
            />
          </div>
        </div>
        <div className="text-sm  w-full gap-2 ">
          <h4 className="w-full text-md mb-1 text-gray-subHeading my-auto">
            Select Job Stop Type :
          </h4>
          <div className="flex items-center w-full">
            <Select
              placeholder="Job Stop"
              value={defaults?.projectDefaults?.defaultJobStopType}
              onChange={(e) => {
                setDefaults((prev) => ({
                  ...prev,
                  projectDefaults: {
                    ...prev.projectDefaults,
                    defaultJobStopType: e.target.value,
                  },
                }));
              }}
              options={['Automatic', 'Manual', 'None']?.map((name) => ({
                name: name,
                value: name.toLocaleLowerCase(),
              }))}
            />
          </div>
        </div>
      </div>

      <div className="mt-6">
        <div className="flex items-center justify-between">
          <h3 className="text-gray-subHeading">Location Defaults:</h3>
          <Button
            className="px-6 mb-4 !h-7 !text-xs"
            onClick={() => setIsLocationModalOpen(true)}
          >
            Add
          </Button>
        </div>
        {isLocationModalOpen && (
          <Modal
            svg={<PuzzlePieceIcon className="w-5 h-5" />}
            title="Add Location Name"
            description="You can add location name for control unit"
            onCloseModal={() => setIsLocationModalOpen(false)}
            onSubmit={handleSubmit}
            btnIsLoading={isLoading}
          >
            {() => {
              return (
                <div>
                  <div className="location-type-container flex items-center gap-3 mb-3 flex-wrap">
                    {locationsType?.map((each) => {
                      return (
                        <label
                          htmlFor={each}
                          key={each}
                          className="flex items-center gap-2 font-semibold"
                        >
                          <input
                            type="checkbox"
                            onChange={(e) => {
                              if (e.target.checked) {
                                setInputs((prev) => {
                                  return {
                                    ...prev,
                                    locationtype: each,
                                  };
                                });
                              }
                            }}
                            checked={
                              inputs.locationtype === each ? true : false
                            }
                            name={each}
                            className="inline-block w-[20px] h-[20px] cursor-pointer"
                          />
                          {each}
                        </label>
                      );
                    })}
                  </div>
                  <label className="text-sm text-gray-400 mb-2">
                    Location Name
                  </label>
                  <NewInput
                    placeholder="Enter Location Name"
                    className="max-w-sm"
                    value={inputs.locationName}
                    onChange={handleInputChange}
                    name="locationName"
                  />
                </div>
              );
            }}
          </Modal>
        )}
        {allLocations?.length > 0 && (
          <Table>
            <Table.Head>
              <Table.Row>
                <Table.Th>LOCATION NAME</Table.Th>
                <Table.Th>LOCATION TYPE</Table.Th>
                <Table.Th>DATE</Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {allLocations?.map((el) => {
                return (
                  <Table.Row key={el?.locationName}>
                    <Table.Td>{el?.locationName}</Table.Td>
                    <Table.Td>{el?.locationtype}</Table.Td>
                    <Table.Td>{getLocalDateTime(el?.createdAt)}</Table.Td>
                  </Table.Row>
                );
              })}
            </Table.Body>
          </Table>
        )}
      </div>
    </div>
  );
};

export default ProjectDefaults;

// send code below
