import { ArrowLeftOutlined } from '@ant-design/icons';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import usePrefixIds from '../../../hooks/usePrefixIds';
import CustomTypesModal from '../global/CustomTypesModal';

import { But<PERSON>, DatePicker, Form } from 'antd';
import dayjs from 'dayjs';
import Input from '../../../components/global/components/Input';
import SelectV2 from '../../../components/global/components/SelectV2';
import Textarea from '../../../components/global/components/Textarea';
import { renderFieldsBasedOnType } from '../../../helperFunction';
import { useGetCustomTypesQuery } from '../../../slices/AccountManagement/customTypesApiSlice';
import {
  useCreateVoucherMutation,
  useEditVoucherMutation,
  useGetVoucherByIdQuery,
} from '../../../slices/AccountManagement/voucherApiSlice';
import { useGetAllcustomerQuery } from '../../../slices/customerDataSlice';
import { useLazyQueryTemplateByIdQuery } from '../../../slices/dsahboardTemplateApiSlice';
import { useGetAllVendorsForOptionsQuery } from '../../../slices/vendorApiSlice';

const PaymentVoucherForm = ({ props }) => {
  const { setOpenModal, editData, setEditData } = props;

  const [editVoucher] = useEditVoucherMutation();
  const { data: ledgerTypes, isLoading: isLedgerTypesLoading } =
    useGetCustomTypesQuery({ type: 'ledgerType' });
  const { data: vendors, isLoading: isVendorLoading } =
    useGetAllVendorsForOptionsQuery();
  const { data: customers } = useGetAllcustomerQuery();
  const { data: voucher } = useGetVoucherByIdQuery(
    { id: editData?._id },
    { skip: editData?._id === undefined }
  );

  const isEditing = editData?._id !== undefined;
  const [createVoucher] = useCreateVoucherMutation();

  const [additionalFields, setAdditionalFields] = useState(null);
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();

  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
  });

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'paymentVoucherId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
    setIdData: setFormData,
  });

  const [openCustomTypeModal, setOpenCustomTypeModal] = useState(false);

  const changeHandler = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  useEffect(() => {
    const path = '/accountmanagement/voucher/paymentVoucher';
    getTemplates({ path });
  }, [getTemplates]);

  useEffect(() => {
    if (voucher?._id && voucher?.paymentVoucherData?.additionalFields) {
      setAdditionalFields(voucher.paymentVoucherData.additionalFields);
      setSelectedTemplate(voucher.paymentVoucherData.additionalFields);
    } else if (templatesData && Array.isArray(templatesData)) {
      const defaultTemplate =
        templatesData.find((t) => t?.name?.startsWith('Default')) ||
        templatesData?.[0];
      if (defaultTemplate) {
        setAdditionalFields(defaultTemplate);
        setSelectedTemplate(defaultTemplate);
      }
    }
  }, [
    voucher?._id,
    voucher?.paymentVoucherData?.additionalFields,
    templatesData,
  ]);

  // Pre-fill form data when editing
  useEffect(() => {
    if (voucher?._id) {
      const vendorId = voucher?.paymentVoucherData?.vendor?._id;
      const customerId = voucher?.paymentVoucherData?.customer?._id;
      let date =
        voucher?.date !== undefined
          ? new Date(voucher?.date)?.toISOString()?.split('T')?.[0]
          : '';
      setFormData({
        date: date,
        paymentVoucherId: voucher?.paymentVoucherId,
        ledgerType: voucher?.paymentVoucherData?.ledgerType?._id,
        vendor: vendorId,
        customer: customerId,
        vendorCustomer: vendorId || customerId,
        paymentMode: voucher?.paymentVoucherData?.paymentMode,
        paidTo: voucher?.paymentVoucherData?.paidTo,
        amount: voucher?.paymentVoucherData?.amount,
        remarks: voucher?.remarks,
        additionalFields: voucher?.paymentVoucherData?.additionalFields || null,
      });

      // Set additional fields if they exist
      if (voucher?.paymentVoucherData?.additionalFields) {
        setAdditionalFields(voucher.paymentVoucherData.additionalFields);
        setSelectedTemplate(voucher.paymentVoucherData.additionalFields);
      }
    }
  }, [voucher]);

  const vendorCustomerOptions = useMemo(() => {
    let options = [];
    if (vendors?.length > 0) {
      let temp = vendors.map((vendor) => ({
        label: vendor.name,
        value: vendor._id,
        type: 'vendor',
      }));
      options = [...options, ...temp];
    }
    if (customers?.customers?.length > 0) {
      let temp = customers.customers.map((customer) => ({
        label: customer.name,
        value: customer._id,
        type: 'customer',
      }));
      options = [...options, ...temp];
    }
    return options;
  }, [vendors, customers]);

  const handleLedgerTypeChange = (value) => {
    if (value === 'addType') {
      setOpenCustomTypeModal(true);
      setFormData((prev) => ({ ...prev, ledgerType: '' }));
    } else {
      setFormData((prev) => ({ ...prev, ledgerType: value }));
    }
  };

  const handleVendorCustomerChange = (selectedId) => {
    const foundVendor = vendors?.find((v) => v?._id === selectedId);
    const foundCustomer = customers?.customers?.find(
      (c) => c?._id === selectedId
    );

    if (foundVendor) {
      setFormData((prev) => ({
        ...prev,
        vendorCustomer: selectedId,
        vendor: selectedId,
        customer: undefined,
      }));
    } else if (foundCustomer) {
      setFormData((prev) => ({
        ...prev,
        vendorCustomer: selectedId,
        customer: selectedId,
        vendor: undefined,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        vendorCustomer: undefined,
        vendor: undefined,
        customer: undefined,
      }));
    }
  };

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }

    if (fieldValue === '+') {
      setDropdownIdx(idx);
      setTemplateDropDownModal(true);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field) => {
          if (field?.fieldName === fieldName) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );
      setAdditionalFields((prev) => ({
        ...prev,
        templateData: updatedAdditionalFields,
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      const chosenId = formData.vendorCustomer;
      const isVendor = vendors?.some((v) => v._id === chosenId);
      const isCustomer = customers?.customers?.some((c) => c._id === chosenId);

      const submitData = {
        ...formData,
        date: formData.date
          ? new Date(formData.date).toISOString()
          : new Date().toISOString(),
        ledgerType: formData.ledgerType,
        vendor: isVendor ? chosenId : undefined,
        customer: isCustomer ? chosenId : undefined,
        paymentMode: formData.paymentMode,
        paidTo: formData.paidTo,
        amount: Number(formData.amount),
        additionalFields:
          additionalFields || formData?.additionalFields || null,
        remarks: formData.remarks,
        voucherType: 'paymentVoucher',
        idData: idCompData?.dataToReturn,
      };

      let res;
      if (editData?._id) {
        res = await editVoucher({
          data: { updateData: submitData, id: editData._id },
        });
      } else {
        res = await createVoucher({ data: submitData });
      }

      if (!res?.error) {
        setOpenModal(false);
        setFormData({
          date: new Date().toISOString().split('T')[0],
        });
        toast.success(
          `Payment Voucher ${editData?._id ? 'Updated' : 'Created'} successfully`
        );
      } else {
        toast.error(
          'Faced an error while creating voucher, please reload and try again.'
        );
      }
    } catch (error) {
      toast.error('An error occurred while processing the form.');
    }
  };

  return (
    <>
      {openCustomTypeModal && (
        <CustomTypesModal
          type="ledgerType"
          openModal={openCustomTypeModal}
          setOpenModal={setOpenCustomTypeModal}
        />
      )}

      <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
        {/* Header */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                setOpenModal(false);
                setEditData({});
              }}
              type="text"
              size="small"
              className="hover:bg-gray-200"
            />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-0">
                {isEditing ? 'Edit' : 'Create'} Payment Voucher
              </h2>
              <p className="text-sm text-gray-600 mb-0">
                {isEditing
                  ? 'Update payment voucher information'
                  : 'Create a new payment voucher'}
              </p>
            </div>
          </div>
        </div>

        <Form layout="vertical" onFinish={handleSubmit}>
          <div className="p-4 space-y-4">
            {/* Basic Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Payment Voucher ID
                  </label>
                  {isEditing ? (
                    <Input
                      disabled
                      className="text-sm bg-gray-50"
                      value={formData?.paymentVoucherId}
                    />
                  ) : (
                    <IdGenComp {...idCompData} />
                  )}
                </div>

                <div className="space-y-1">
                  <label className="block mb-1 text-sm text-gray-500 font-medium">
                    Choose Template
                  </label>
                  <SelectV2
                    options={templatesData?.map((template) => ({
                      value: template?._id,
                      name: template?.name,
                    }))}
                    value={selectedTemplate?._id}
                    onChange={(e) => {
                      const template = templatesData.find(
                        (t) => t._id === e.target.value
                      );
                      if (selectedTemplate?._id === e.target.value) {
                        return;
                      }
                      setAdditionalFields(template);
                      setSelectedTemplate(template);
                    }}
                  />
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Date
                  </label>
                  <DatePicker
                    format="DD-MM-YYYY"
                    className="text-sm placeholder:text-gray-400 w-full"
                    placeholder="Select date"
                    name="date"
                    value={formData?.date ? dayjs(formData.date) : null}
                    onChange={(e) =>
                      changeHandler({
                        target: {
                          name: 'date',
                          value: e?.format('YYYY-MM-DD'),
                        },
                      })
                    }
                  />
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Ledger Type
                  </label>
                  <SelectV2
                    placeholder="Select ledger type"
                    loading={isLedgerTypesLoading}
                    onChange={(e) => handleLedgerTypeChange(e.target.value)}
                    name="ledgerType"
                    value={formData.ledgerType}
                    className="text-sm"
                    menuPosition="fixed"
                    options={[
                      { name: '+ Add Type', value: 'addType' },
                      ...(ledgerTypes?.map((item) => ({
                        name: item.name,
                        value: item._id,
                      })) || []),
                    ]}
                  />
                </div>
              </div>
            </div>

            {/* Vendor/Customer */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Vendor/Customer Selection
              </h3>
              <div className="grid grid-cols-1 gap-3">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Vendor/Customer
                  </label>
                  <SelectV2
                    placeholder="Select vendor or customer"
                    loading={isVendorLoading}
                    onChange={(e) => handleVendorCustomerChange(e.target.value)}
                    name="vendorCustomer"
                    value={formData.vendorCustomer}
                    className="text-sm"
                    menuPosition="fixed"
                    options={vendorCustomerOptions.map((option) => ({
                      label: option.label,
                      value: option.value,
                    }))}
                  />
                </div>
              </div>
            </div>
            {/* Payment Details */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Payment Details
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Payment Mode
                  </label>
                  <Input
                    placeholder="Enter payment mode"
                    className="text-sm"
                    name="paymentMode"
                    value={formData.paymentMode}
                    onChange={(e) => changeHandler(e)}
                  />
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Paid To
                  </label>
                  <Input
                    placeholder="Enter paid to"
                    className="text-sm"
                    name="paidTo"
                    value={formData.paidTo}
                    onChange={(e) => changeHandler(e)}
                  />
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Amount
                  </label>
                  <Input
                    type="number"
                    placeholder="Enter amount"
                    className="text-sm"
                    name="amount"
                    value={formData.amount}
                    onChange={(e) => changeHandler(e)}
                  />
                </div>
              </div>
            </div>

            {/* Template Details */}
            {additionalFields?.templateData?.length > 0 && (
              <div className="bg-white border border-gray-200 rounded-lg p-3">
                <h3 className="text-sm font-medium text-gray-700 mb-3">
                  Template Details
                </h3>
                <section className="w-full">
                  {renderFieldsBasedOnType(
                    additionalFields,
                    handleInputChange,
                    templateDropDownModal,
                    setTemplateDropDownModal,
                    setAdditionalFields,
                    newOptionStatus,
                    setNewOptionStatus,
                    dropdownIdx,
                    setDropdownIdx
                  )}
                </section>
              </div>
            )}

            {/* Remarks */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Remarks
              </h3>
              <Textarea
                name="remarks"
                value={formData.remarks}
                onChange={(e) => changeHandler(e)}
                rows={3}
                placeholder="Enter additional remarks or comments"
                className="text-sm resize-none"
              />
            </div>
          </div>

          {/* Footer Actions */}
          <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
            <div className="flex items-center justify-end gap-2">
              <Button
                onClick={() => {
                  setOpenModal(false);
                  setEditData({});
                }}
                size="small"
                className="text-sm px-4 py-1 h-8"
              >
                Cancel
              </Button>
              <Button
                htmlType="submit"
                type="primary"
                size="small"
                className="text-sm px-4 py-1 h-8"
              >
                {isEditing ? 'Update' : 'Save'} Payment Voucher
              </Button>
            </div>
          </div>
        </Form>
      </div>
    </>
  );
};

export default PaymentVoucherForm;
