import { apiSlice } from '../apiSlice';

const baseRoute = '/v1/hrms/payroll';
export const masterAccountApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    queryPayroll: builder.query({
      query: ({
        page,
        limit,
        field = '',
        type = '',
        // filters,
        // searchTerm = '',
      }) =>
        baseRoute + `?page=${page}&limit=${limit}&field=${field}&type=${type}`,
      providesTags: ['Payroll'],
    }),
    createPayroll: builder.mutation({
      query: ({ data }) => ({
        url: baseRoute,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Payroll'],
    }),
    deletePayroll: builder.mutation({
      query: ({ id }) => ({
        url: baseRoute + `/delete/${id}`,
        method: 'GET',
      }),
      invalidatesTags: ['Payroll'],
    }),
    getPayrollById: builder.query({
      query: ({ id }) => baseRoute + `/${id}`,
      providesTags: ['Payroll'],
    }),
    runPayroll: builder.mutation({
      query: ({ id, data }) => ({
        url: baseRoute + `/run/${id}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Payroll', 'User'],
    }),
  }),
});

export const {
  useQueryPayrollQuery,
  useCreatePayrollMutation,
  useGetPayrollByIdQuery,
  useRunPayrollMutation,
  useDeletePayrollMutation,
} = masterAccountApi;
