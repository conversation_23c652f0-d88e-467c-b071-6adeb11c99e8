import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Button, Modal, Space, Table, Tooltip } from 'antd';
import { useState } from 'react';
import { toast } from 'react-toastify';
import Pagination from '../../../components/global/components/Pagination';
import SelectV2 from '../../../components/global/components/SelectV2';
import {
  useDeleteInvoiceMutation,
  useUpdateInvoiceStatusMutation,
} from '../../../slices/AccountManagement/invoicesApiSlice';
import { customConfirm } from '../../../utils/customConfirm';
import InvoiceSidebar from './InvoiceSidebar';

const PurchaseInvoiceTab = ({
  invoices,
  onEdit,
  isLoading,
  total,
  totalPages,
  limit,
  page,
  setPage,
  setLimit,
}) => {
  const [openSideBar, setOpenSideBar] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentInvoiceId, setCurrentInvoiceId] = useState(null);
  const [currentStatus, setCurrentStatus] = useState('draft');

  const [updateInvoiceStatus] = useUpdateInvoiceStatusMutation();
  const [deleteInvoice] = useDeleteInvoiceMutation();

  const statusOptions = ['draft', 'sent', 'paid', 'overdue', 'cancelled'];

  const handleStatusChange = async (newStatus) => {
    try {
      await updateInvoiceStatus({ id: currentInvoiceId, status: newStatus });
      toast.success('Invoice status updated successfully');
      setIsModalVisible(false);
    } catch (error) {
      toast.error('Failed to update invoice status');
    }
  };

  const showModal = (invoiceId, status) => {
    setCurrentInvoiceId(invoiceId);
    setCurrentStatus(status);
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleClick = (id) => {
    setOpenSideBar(true);
    setSelectedInvoiceId(id);
  };

  const handleDelete = async (invoiceId) => {
    const confirm = await customConfirm(
      'Are you sure you want to delete?',
      'error'
    );
    if (!confirm) return;
    const res = await deleteInvoice({ id: invoiceId });
    if (res.error) {
      toast.error('Failed to delete invoice. Please reload and try again.');
    } else {
      toast.success('Invoice deleted successfully');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-200 text-gray-800';
      case 'sent':
        return 'bg-blue-200 text-blue-800';
      case 'paid':
        return 'bg-green-200 text-green-800';
      case 'overdue':
        return 'bg-red-200 text-red-800';
      case 'cancelled':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-gray-200 text-gray-800';
    }
  };

  const columns = [
    {
      title: 'Purchase Invoice ID',
      dataIndex: 'purchaseInvoiceId',
      key: 'purchaseInvoiceId',
      render: (invoiceId, record) => (
        <span
          className="font-medium text-blue-600 cursor-pointer hover:underline"
          onClick={() => handleClick(record?._id)}
        >
          {invoiceId || 'N/A'}
        </span>
      ),
    },
    {
      title: 'Vendor',
      key: 'vendor',
      render: (_, record) => (
        <span
          className="font-medium text-gray-900"
          onClick={() => handleClick(record?._id)}
        >
          {record.vendor?.[0]?.name ||
            record.purchaseOrderRef?.vendor?.name ||
            'N/A'}
        </span>
      ),
    },
    {
      title: 'Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => (
        <span className="font-semibold text-red-600">₹{amount || 0}</span>
      ),
      sorter: (a, b) => (a.totalAmount || 0) - (b.totalAmount || 0),
    },
    {
      title: 'Invoice Date',
      dataIndex: 'invoiceDate',
      key: 'invoiceDate',
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: (a, b) =>
        new Date(a.invoiceDate).getTime() - new Date(b.invoiceDate).getTime(),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
          {type?.charAt(0).toUpperCase() + type?.slice(1) || 'N/A'}
        </span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium cursor-pointer ${getStatusColor(
            status
          )}`}
          onClick={() => showModal(record._id, status)}
        >
          {status?.charAt(0).toUpperCase() + status?.slice(1) || 'N/A'}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 60,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Edit Ledger">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => {
                onEdit(record._id);
              }}
              style={{ color: '#52c41a' }}
            />
          </Tooltip>
          <Tooltip title="Delete Ledger">
            <Button
              type="text"
              icon={<DeleteOutlined />}
              size="small"
              danger
              onClick={() => handleDelete(record._id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <Table
        columns={columns}
        dataSource={invoices}
        rowKey="_id"
        pagination={false}
        scroll={{ x: true }}
        loading={isLoading}
      />
      <Pagination
        limit={limit}
        page={page}
        totalPages={totalPages}
        totalResults={total}
        setPage={setPage}
        setLimit={setLimit}
        className={`w-full`}
      />
      <Modal
        title="Change Invoice Status"
        open={isModalVisible}
        onOk={() => handleStatusChange(currentStatus)}
        onCancel={handleCancel}
        footer={[
          <Button key="back" onClick={handleCancel}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => handleStatusChange(currentStatus)}
          >
            OK
          </Button>,
        ]}
      >
        <SelectV2
          value={currentStatus}
          onChange={(e) => setCurrentStatus(e.target.value)}
          size="small"
          menuPosition="fixed"
          className="w-full"
          options={statusOptions?.map((el) => ({
            label: el.charAt(0).toUpperCase() + el.slice(1),
            value: el,
          }))}
        />
      </Modal>
      {openSideBar && (
        <InvoiceSidebar
          id={selectedInvoiceId}
          openSidebar={openSideBar}
          setOpenSidebar={setOpenSideBar}
          onEdit={onEdit}
          type="purchase"
        />
      )}
    </div>
  );
};

export default PurchaseInvoiceTab;
