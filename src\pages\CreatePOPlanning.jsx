import { useParams } from 'react-router-dom';
import {
  Input,
  Button,
  Modal,
  Table,
  Space,
  Typography,
  Divider,
  Tag,
  Tooltip,
  Spin,
} from 'antd';
import { useState, useEffect } from 'react';
import {
  useGetBomsForWoOptionsQuery,
  useLazyGetItemsForPoPlanningQuery,
} from '../slices/assemblyBomApiSlice';
import SelectV2 from '../components/global/components/SelectV2';
import MultiSelect from '../components/global/components/MultiSelect';
import {
  EyeOutlined,
  SaveOutlined,
  ArrowLeftOutlined,
  ShoppingCartOutlined,
  FileTextOutlined,
  ContactsOutlined,
  PlusOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import usePrefixIds from '../hooks/usePrefixIds';
import { toast } from 'react-toastify';
import {
  useCreatePurchasePlanningMutation,
  useGetPurchasePlanningQuery,
  useUpdatePurchasePlanningMutation,
  useCreatePOForPlanningMutation,
} from '../slices/purchasePlanningApiSlice';
import { useNavigate } from 'react-router-dom';
import Header from '../components/global/components/Header';
import VariantSelector from '../components/global/VariantSelector';
import { useLazyGetVariantsForPlanningQuery } from '../slices/variantApiSlice';

const { Title, Text } = Typography;

const LoadingOverlay = () => (
  <div
    style={{
      position: 'fixed',
      top: 0,
      left: 0,
      height: '100vh',
      width: '100vw',
      backgroundColor: 'rgba(255, 255, 255, 0.6)',
      zIndex: 9999,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    }}
  >
    <Spin size="large" tip="Processing, please wait..." />
  </div>
);

const maxQuantityCheck = (quantity, baseQuantity) => {
  if (quantity > baseQuantity) {
    toast.error(`Quantity should be less than or equal to ${baseQuantity}`);
    return false;
  }
  return true;
};

const BOMSelectorModal = ({ formData, setFormData, open, setOpen }) => {
  const { data: bomOptions } = useGetBomsForWoOptionsQuery();
  const [getItemsForPoPlanning, { isLoading: bomfetching }] =
    useLazyGetItemsForPoPlanningQuery();

  const handleBomSubmit = async () => {
    if (!formData.bom) {
      toast.error('Please select BOM');
      return;
    }
    if (!formData.baseQuantity) {
      toast.error('Please enter base quantity');
      return;
    }
    const res = await getItemsForPoPlanning({
      id: formData.bom,
      baseQuantity: formData.baseQuantity,
    }).unwrap();
    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, ...res],
    }));
    setOpen(false);
  };

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <FileTextOutlined className="text-blue-500" />
          <span>Select BOM</span>
        </div>
      }
      open={open}
      onCancel={() => setOpen(false)}
      onOk={handleBomSubmit}
      confirmLoading={bomfetching}
      width={500}
    >
      <div className="mt-6">
        <label className="block text-sm font-semibold text-gray-700 mb-2">
          <span className="text-red-500 mr-1">*</span>
          Select BOM
        </label>
        <SelectV2
          placeholder="Choose a BOM"
          value={formData.bom}
          onChange={(e) => {
            setFormData((prev) => ({
              ...prev,
              bom: e.target.value,
            }));
          }}
          name="bom"
          options={bomOptions?.map((bom) => ({
            label: bom.name,
            value: bom._id,
          }))}
        />
      </div>
    </Modal>
  );
};

const ExistingPOIndentModal = ({ open, setOpen, data }) => {
  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <ShoppingCartOutlined className="text-green-500" />
          <span>Existing PO/Indent</span>
        </div>
      }
      open={open}
      footer={null}
      onCancel={() => setOpen(false)}
      confirmLoading={false}
      width={900}
      styles={{
        body: {
          maxHeight: `calc(100vh - 200px)`,
          overflowY: 'auto',
        },
      }}
    >
      <div className="mt-4">
        {data?.length > 0 ? (
          <Table
            scroll={{ x: true }}
            columns={[
              {
                title: 'Type',
                dataIndex: 'type',
                key: 'type',
                render: (type) => (
                  <Tag color={type === 'PO' ? 'blue' : 'orange'}>{type}</Tag>
                ),
              },
              {
                title: 'Quantity',
                dataIndex: 'quantity',
                key: 'quantity',
                render: (qty) => <Text strong>{qty}</Text>,
              },
              {
                title: 'Status',
                dataIndex: 'status',
                key: 'status',
                render: (status) => (
                  <Tag color={status === 'Active' ? 'green' : 'red'}>
                    {status}
                  </Tag>
                ),
              },
              {
                title: 'PO/Indent No',
                dataIndex: 'prefixId',
                key: 'prefixId',
                render: (id) => <Text code>{id}</Text>,
              },
              {
                title: 'Vendor Name',
                dataIndex: 'vendorName',
                key: 'vendorName',
                render: (name) => <Text strong>{name}</Text>,
              },
            ]}
            dataSource={data}
            pagination={false}
            size="small"
          />
        ) : (
          <div className="text-center py-8">
            <Text type="secondary">No existing PO/Indent found</Text>
          </div>
        )}
      </div>
    </Modal>
  );
};

const VendorContactModal = ({ open, setOpen, data }) => {
  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <ContactsOutlined className="text-purple-500" />
          <span>Vendor Contact Details</span>
        </div>
      }
      open={open}
      footer={null}
      onCancel={() => setOpen(false)}
      confirmLoading={false}
      width={600}
    >
      <div className="space-y-4 mt-4">
        <div>
          <Text strong className="text-gray-600">
            Name:
          </Text>
          <div className="mt-1">
            <Text>{data?.vendorName || '-'}</Text>
          </div>
        </div>
        <Divider />
        <div>
          <Text strong className="text-gray-600">
            Address:
          </Text>
          <div className="mt-1">
            <Text>
              {Array.isArray(data?.address) && data.address.length > 0
                ? data.address.join(', ')
                : '-'}
            </Text>
          </div>
        </div>
        <Divider />
        <div>
          <Text strong className="text-gray-600">
            Contact:
          </Text>
          <div className="mt-1">
            <Text>
              {Array.isArray(data?.contact) && data.contact.length > 0
                ? data.contact.join(', ')
                : '-'}
            </Text>
          </div>
        </div>
        <Divider />
        <div>
          <Text strong className="text-gray-600">
            Email:
          </Text>
          <div className="mt-1">
            <Text>
              {Array.isArray(data?.email) && data.email.length > 0
                ? data.email.join(', ')
                : '-'}
            </Text>
          </div>
        </div>
      </div>
    </Modal>
  );
};

const ItemSelectorModal = ({ open, setOpen, setFormData, formData }) => {
  const [manualAddedItems, setManualAddedItems] = useState([]);
  const [selectorValue, setSelectorValue] = useState([]);
  const [getVariantsForPlanning, { isFetching }] =
    useLazyGetVariantsForPlanningQuery();

  const handleAddManualItem = async (itemIds) => {
    setSelectorValue(itemIds);
    const existingIds = manualAddedItems.map((item) => item.itemId?.toString());
    const newId = itemIds.find((id) => !existingIds.includes(id?.toString()));
    if (!newId) return;

    const res = await getVariantsForPlanning({ id: newId });
    const fetchedItem = res?.data;

    if (fetchedItem) {
      setManualAddedItems((prev) => [...prev, fetchedItem]);
    }
  };

  const handleModalSubmit = () => {
    const multipliedItems = manualAddedItems.map((item) => ({
      ...item,
      requiredQuantity: item?.requiredQuantity * formData?.baseQuantity,
    }));
    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, ...multipliedItems],
    }));
    setManualAddedItems([]);
    setSelectorValue([]);
    setOpen(false);
  };

  const handleFilterItem = (id) => {
    setManualAddedItems((prev) => prev.filter((item) => item?.itemId !== id));
    setSelectorValue((prev) => prev.filter((item) => item !== id));
  };

  const handleSetRequiredQuantity = (id, e) => {
    const value = maxQuantityCheck(e.target.value, 100000000);
    if (!value) return;
    setManualAddedItems((prev) =>
      prev.map((item) =>
        item?.itemId === id
          ? {
              ...item,
              requiredQuantity: parseFloat(e.target.value) || e.target.value,
            }
          : item
      )
    );
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'itemName',
      key: 'itemname',
    },
    {
      title: 'Available Quantity',
      dataIndex: 'availableQuantity',
      key: 'availableQuantity',
    },
    {
      title: 'Type',
      dataIndex: 'itemType',
      key: 'itemType',
    },
    {
      title: 'Required Quantity',
      dataIndex: 'requiredQuantity',
      key: 'requiredQuantity',
      render: (_, record) => (
        <Input
          size="small"
          type="number"
          value={record?.requiredQuantity ?? 0}
          onChange={(e) => handleSetRequiredQuantity(record.itemId, e)}
        />
      ),
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      render: (_, record) => (
        <Button
          danger
          size="small"
          icon={<DeleteOutlined />}
          onClick={() => handleFilterItem(record.itemId)}
        />
      ),
    },
  ];

  const resetModal = () => {
    setOpen(false);
    setManualAddedItems([]);
    setSelectorValue([]);
  };
  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <ShoppingCartOutlined className="text-green-500" />
          <span>Item Selector</span>
        </div>
      }
      open={open}
      onOk={handleModalSubmit}
      onCancel={resetModal}
      confirmLoading={false}
      width={900}
    >
      <div className="mt-4">
        <VariantSelector
          value={selectorValue}
          onItemAdd={handleAddManualItem}
          disableAddButton={isFetching}
        />
        <Table
          columns={columns}
          dataSource={manualAddedItems}
          loading={isFetching}
          pagination={false}
          size="small"
        />
      </div>
    </Modal>
  );
};

const CreatePOPlanning = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = !!id;
  const [openBOMSelector, setOpenBOMSelector] = useState(false);
  const [existingPOIndentModal, setExistingPOIndentModal] = useState(false);
  const [existingPOIndentData, setExistingPOIndentData] = useState(null);
  const [showVendorContactModal, setShowVendorContactModal] = useState(false);
  const [vendorContactDetails, setVendorContactDetails] = useState(null);
  const [createPoLoadingForIndex, setCreatePoLoadingForIndex] = useState({});
  const [openItemSelector, setOpenItemSelector] = useState(false);

  const [createPoPlanning, { isLoading: isCreatePoPlanningLoading }] =
    useCreatePurchasePlanningMutation();
  const { data: editData, isLoading: isGetPoPlanningLoading } =
    useGetPurchasePlanningQuery(id, {
      skip: !isEditMode,
    });
  const [updatePoPlanning, { isLoading: isUpdatePoPlanningLoading }] =
    useUpdatePurchasePlanningMutation();
  const [createPoForPlanning] = useCreatePOForPlanningMutation();

  const [formData, setFormData] = useState({
    poPlanningId: '',
    name: '',
    items: [],
    bom: null,
  });

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'poPlanningId',
    setIdData: setFormData,
  });

  const isSaving =
    isCreatePoPlanningLoading ||
    isUpdatePoPlanningLoading ||
    isGetPoPlanningLoading;

  useEffect(() => {
    if (isEditMode && editData) {
      setFormData(editData);
    }
  }, [isEditMode, editData]);

  const handleVendorChange = (
    itemIndex,
    selectedVendors,
    allVendorsDetails,
    isRawMaterial = false,
    rmIndex = null
  ) => {
    setFormData((prev) => {
      const newItems = [...prev.items];
      const selectedVendorsDetails = allVendorsDetails.filter((vd) =>
        selectedVendors.some((sel) => sel.value === vd.vendorId)
      );

      if (isRawMaterial) {
        const updatedRawMaterial = {
          ...newItems[itemIndex].rawMaterials[rmIndex],
          selectedVendors,
          selectedVendorsDetails,
        };
        const updatedRawMaterials = [...newItems[itemIndex].rawMaterials];
        updatedRawMaterials[rmIndex] = updatedRawMaterial;
        newItems[itemIndex] = {
          ...newItems[itemIndex],
          rawMaterials: updatedRawMaterials,
        };
      } else {
        const item = {
          ...newItems[itemIndex],
          selectedVendors,
          selectedVendorsDetails,
        };
        if (item.rawMaterials?.length > 0) {
          const totalReqQty = item.toPurchaseQty;
          const totalUnits = item.rawMaterials.reduce(
            (a, rm) => a + (rm.requiredQuantity || 0),
            0
          );
          const updatedRawMaterials = item.rawMaterials.map((rm) => ({
            ...rm,
            toPurchaseQty:
              totalUnits > 0
                ? (rm.requiredQuantity / totalUnits) * totalReqQty
                : 0,
          }));
          item.rawMaterials = updatedRawMaterials;
        }
        newItems[itemIndex] = item;
      }

      return {
        ...prev,
        items: newItems,
      };
    });
  };

  const handleChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const getTotalPriceForRecord = (record) => {
    let totalPrice = 0;
    if (!record?.rawMaterials?.length) {
      if (record?.selectedVendorsDetails?.length > 0) {
        totalPrice = record.selectedVendorsDetails.reduce((acc, vendor) => {
          const quantity = Number.parseFloat(vendor.quantity) || 0;
          const rate = Number.parseFloat(vendor.rate) || 0;
          return acc + quantity * rate;
        }, 0);
      }
      return totalPrice;
    }

    if (record?.rawMaterials?.length > 0) {
      totalPrice = record.rawMaterials.reduce((acc, rawMaterial) => {
        if (!rawMaterial?.selectedVendorsDetails?.length) return acc;
        const rmTotal = rawMaterial.selectedVendorsDetails.reduce(
          (subAcc, vendor) => {
            const quantity = Number.parseFloat(vendor.quantity) || 0;
            const rate = Number.parseFloat(vendor.rate) || 0;
            return subAcc + quantity * rate;
          },
          0
        );
        return acc + rmTotal;
      }, 0);
      return totalPrice;
    }
    return 0;
  };

  const getPoIsDisabled = (record) => {
    if (Array.isArray(record?.rawMaterials) && record.rawMaterials.length > 0) {
      return record.rawMaterials.some((rm) => {
        if (
          !Array.isArray(rm?.selectedVendorsDetails) ||
          rm.selectedVendorsDetails.length === 0
        ) {
          return true;
        }
        return rm.selectedVendorsDetails.some(
          (vd) =>
            !Number.parseFloat(vd.quantity) ||
            Number.parseFloat(vd.quantity) <= 0
        );
      });
    }
    if (
      Array.isArray(record?.selectedVendorsDetails) &&
      record.selectedVendorsDetails.length > 0
    ) {
      return record.selectedVendorsDetails.some(
        (vd) =>
          !Number.parseFloat(vd.quantity) || Number.parseFloat(vd.quantity) <= 0
      );
    }
    if (
      !record?.selectedVendorsDetails?.length &&
      !record?.rawMaterials?.length
    ) {
      return true;
    }
    return false;
  };

  const handleSave = async () => {
    if (!formData?.name) {
      toast.error('Please enter name');
      return;
    }
    if (!formData?.items?.length) {
      toast.error('Please add items');
      return;
    }
    if (isEditMode) {
      const { _id, ...rest } = formData;
      const res = await updatePoPlanning({ id: _id, data: rest }).unwrap();
      if (res) {
        toast.success('PO Planning updated successfully');
      }
    } else {
      const res = await createPoPlanning({ data: formData }).unwrap();
      if (res) {
        toast.success('PO Planning created successfully');
        navigate(`/purchase/planning/edit/${res?.created?._id}`);
      }
    }
  };

  const handleCreatePO = async (record, itemIndex) => {
    if (!formData?.name) {
      toast.error('Please enter name');
      return;
    }
    setCreatePoLoadingForIndex((prev) => ({ ...prev, [itemIndex]: true }));
    const res = await createPoForPlanning({
      item: record,
      formData,
      itemIndex,
    });
    if (!res?.error) {
      toast.success('PO created successfully');
      if (res?.data?.created?._id)
        navigate(`/purchase/planning/edit/${res?.data.created?._id}`);
    }
    setCreatePoLoadingForIndex((prev) => ({ ...prev, [itemIndex]: false }));
  };

  const tableCols = [
    {
      title: 'Item',
      dataIndex: 'itemName',
      key: 'itemName',
      render: (name) => (
        <Text strong className="text-blue-600 whitespace-nowrap">
          {name}
        </Text>
      ),
    },
    {
      title: 'Required Quantity',
      dataIndex: 'requiredQuantity',
      key: 'requiredQuantity',
      render: (qty) => <Tag color="blue">{qty ?? 0}</Tag>,
    },
    {
      title: 'Available Quantity',
      dataIndex: 'availableQuantity',
      key: 'availableQuantity',
      render: (qty) => <Tag color="green">{qty ?? 0}</Tag>,
    },
    {
      title: 'To Purchase',
      dataIndex: 'toPurchaseQty',
      key: 'toPurchaseQty',
      render: (_, record, index) => (
        <Input
          size="small"
          value={record.toPurchaseQty || 0}
          name="toPurchaseQty"
          disabled={record.disabled}
          className="w-24"
          min={0}
          type="number"
          onChange={(e) => {
            const value = maxQuantityCheck(e.target.value, 100000000);
            if (!value) return;
            const inputValue = Number.parseFloat(e.target.value) || 0;
            setFormData((prev) => ({
              ...prev,
              items: prev.items.map((item, idx) => {
                if (idx === index) {
                  return {
                    ...item,
                    toPurchaseQty: inputValue,
                    selectedVendorsDetails: item.selectedVendorsDetails?.map(
                      (vd) => ({
                        ...vd,
                        quantity: 0,
                      })
                    ),
                    rawMaterials: item.rawMaterials?.map((rm) => ({
                      ...rm,
                      selectedVendorsDetails: rm.selectedVendorsDetails?.map(
                        (vd) => ({
                          ...vd,
                          quantity: 0,
                        })
                      ),
                    })),
                  };
                }
                return item;
              }),
            }));
          }}
        />
      ),
    },
    {
      title: 'Existing PO/Indent',
      dataIndex: 'existingPO',
      key: 'existingPO',
      render: (_, record) => {
        const indentsAndPos = [
          ...(record?.indents || []),
          ...(record?.pos || []),
        ];
        return (
          <Tooltip title="View existing PO/Indent">
            <Button
              disabled={indentsAndPos.length === 0}
              type="primary"
              size="small"
              onClick={() => {
                setExistingPOIndentModal(true);
                setExistingPOIndentData(indentsAndPos);
              }}
              icon={<EyeOutlined />}
            />
          </Tooltip>
        );
      },
    },
    {
      title: 'Vendor',
      dataIndex: 'vendor',
      key: 'vendor',
      render: (_, record, index) => {
        if (record?.rawMaterials?.length > 0) {
          return (
            <Text type="secondary" italic>
              Select vendors for raw materials
            </Text>
          );
        }
        return (
          <MultiSelect
            disabled={record.disabled}
            position="fixed"
            value={record.selectedVendors}
            name="vendor"
            options={record?.vendor_details?.map(
              ({ vendorId, vendorName }) => ({
                label: vendorName,
                value: vendorId,
              })
            )}
            getOptionTooltip={(option) => {
              const matchingVendor = record?.vendor_details?.find(
                (vd) => vd.vendorId === option.value
              );
              return (
                <div className="text-xs space-y-1">
                  <p>
                    <strong>Lead Time: </strong>
                    {matchingVendor.leadTime}
                  </p>
                  <p>
                    <strong>Price: </strong>₹{matchingVendor.rate}
                  </p>
                  <p>
                    <strong>Credit Availability: </strong>
                    {matchingVendor.creditAvailability ? 'Yes' : 'No'}
                  </p>
                </div>
              );
            }}
            onChange={(e) => {
              handleVendorChange(index, e.target.value, record?.vendor_details);
            }}
          />
        );
      },
    },
    {
      title: 'Total Price',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      render: (_, record) => (
        <Text strong className="text-green-600 whitespace-nowrap">
          ₹{getTotalPriceForRecord(record).toFixed(2)}
        </Text>
      ),
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      render: (_, record, index) => (
        <Button
          onClick={() => handleCreatePO(record, index)}
          disabled={
            getPoIsDisabled(record) ||
            record.disabled ||
            createPoLoadingForIndex[index]
          }
          type="primary"
          size="small"
          loading={!!createPoLoadingForIndex[index]}
          icon={<ShoppingCartOutlined />}
        />
      ),
    },
  ];

  const rmColumns = [
    {
      title: 'Name',
      dataIndex: 'itemName',
      key: 'itemName',
      render: (name) => (
        <Text strong className="text-purple-600 whitespace-nowrap">
          {name}
        </Text>
      ),
    },
    {
      title: 'Required RM Quantity',
      dataIndex: 'requiredQuantity',
      key: 'requiredQuantity',
      render: (qty) => <Tag color="purple">{qty}</Tag>,
    },
    {
      title: 'Available Stock',
      dataIndex: 'availableQuantity',
      key: 'availableQuantity',
      render: (qty) => <Tag color="cyan">{qty}</Tag>,
    },
    {
      title: 'Select Vendor',
      dataIndex: 'vendor',
      key: 'vendor',
      render: (_, record, index) => (
        <MultiSelect
          disabled={record.disabled}
          value={record.selectedVendors}
          getOptionTooltip={(option) => {
            const matchingVendor = record?.vendor_details?.find(
              (vd) => vd.vendorId === option.value
            );
            return (
              <div className="text-xs space-y-1">
                <p>
                  <strong>Lead Time: </strong>
                  {matchingVendor.leadTime}
                </p>
                <p>
                  <strong>Price: </strong>₹{matchingVendor.rate}
                </p>
                <p>
                  <strong>Credit Availability: </strong>
                  {matchingVendor.creditAvailability ? 'Yes' : 'No'}
                </p>
              </div>
            );
          }}
          name="vendor"
          onChange={(e) => {
            handleVendorChange(
              record.parentRowIndex,
              e.target.value,
              record?.vendor_details,
              true,
              index
            );
          }}
          options={record?.vendor_details?.map(
            ({ vendorId, vendorName, leadTime, rate }) => ({
              label: vendorName,
              value: vendorId,
              leadTime,
              rate,
            })
          )}
        />
      ),
    },
  ];

  const vendorColumns = [
    {
      title: 'Vendor Name',
      dataIndex: 'vendorName',
      key: 'vendorName',
      render: (name) => (
        <Text strong className="text-indigo-600 whitespace-nowrap">
          {name}
        </Text>
      ),
    },
    {
      title: 'Lead Time',
      dataIndex: 'leadTime',
      key: 'leadTime',
      render: (time) => <Tag color="orange">{time} days</Tag>,
    },
    {
      title: 'Rate',
      dataIndex: 'rate',
      key: 'rate',
      render: (rate) => (
        <Text strong className="text-green-600">
          ₹{rate}
        </Text>
      ),
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (_, record, index) => (
        <Input
          size="small"
          value={record.quantity || 0}
          name="quantity"
          type="number"
          disabled={record.disabled}
          className="w-20"
          min={0}
          onChange={(e) => {
            const value = maxQuantityCheck(e.target.value, 100000000);
            if (!value) return;
            const inputQty = Number.parseFloat(e.target.value) || 0;
            setFormData((prev) => {
              const updatedItems = [...prev.items];

              if (record?.fromRm) {
                const item = { ...updatedItems[record.parentRowIndex] };
                const rawMaterials = [...item.rawMaterials];
                const rm = { ...rawMaterials[record.parentRmIndex] };
                const updatedVendors = rm.selectedVendorsDetails.map(
                  (vd, vdIdx) =>
                    vdIdx === index ? { ...vd, quantity: inputQty } : vd
                );
                const totalRmVendorQuantity = updatedVendors.reduce(
                  (acc, vd) => acc + (Number.parseFloat(vd.quantity) || 0),
                  0
                );
                if (totalRmVendorQuantity > rm.requiredQuantity) {
                  toast.error(
                    'Vendor quantity cannot exceed required quantity for raw material.',
                    {
                      theme: 'colored',
                      position: 'top-right',
                      toastId: 'rm-vendor-qty-error',
                    }
                  );
                  return prev;
                }

                rm.selectedVendorsDetails = updatedVendors;
                rawMaterials[record.parentRmIndex] = rm;
                item.rawMaterials = rawMaterials;
                updatedItems[record.parentRowIndex] = item;
              } else {
                const item = { ...updatedItems[record.parentRowIndex] };
                const updatedVendors = item.selectedVendorsDetails.map(
                  (vd, vdIdx) =>
                    vdIdx === index ? { ...vd, quantity: inputQty } : vd
                );
                const totalVendorQuantity = updatedVendors.reduce(
                  (acc, vd) => acc + (Number.parseFloat(vd.quantity) || 0),
                  0
                );
                if (totalVendorQuantity > item.toPurchaseQty) {
                  toast.error('Vendor quantity cannot exceed toPurchaseQty.', {
                    theme: 'colored',
                    position: 'top-right',
                    toastId: 'direct-vendor-qty-error',
                  });
                  return prev;
                }
                item.selectedVendorsDetails = updatedVendors;
                updatedItems[record.parentRowIndex] = item;
              }

              return { ...prev, items: updatedItems };
            });
          }}
        />
      ),
    },
    {
      title: 'Sub Total',
      dataIndex: 'subTotal',
      key: 'subTotal',
      render: (_, record) => (
        <Text strong className="text-green-600">
          ₹
          {(
            (Number(record.rate) || 0) * (Number(record.quantity) || 0)
          ).toFixed(2)}
        </Text>
      ),
    },
    {
      title: 'Credit Availability',
      dataIndex: 'creditAvailability',
      key: 'creditAvailability',
      render: (_, record) => (
        <Tag color={record.creditAvailability ? 'green' : 'red'}>
          {record.creditAvailability ? 'Yes' : 'No'}
        </Tag>
      ),
    },
    {
      title: 'Contact Details',
      dataIndex: 'contactDetails',
      key: 'contactDetails',
      render: (_, record) => (
        <Tooltip title="View contact details">
          <Button
            type="primary"
            size="small"
            onClick={() => {
              setShowVendorContactModal(true);
              setVendorContactDetails(record);
            }}
            icon={<ContactsOutlined />}
          />
        </Tooltip>
      ),
    },
  ];

  function markDisabledDeep(data) {
    return data.map((item) => {
      const disabled = !!item.poCreated;
      const rawMaterials =
        item.rawMaterials?.map((rm) => ({
          ...rm,
          disabled,
          selectedVendorsDetails:
            rm.selectedVendorsDetails?.map((vd) => ({
              ...vd,
              disabled,
            })) || [],
        })) || [];

      return {
        ...item,
        disabled,
        rawMaterials,
        selectedVendorsDetails:
          item.selectedVendorsDetails?.map((vd) => ({
            ...vd,
            disabled,
          })) || [],
      };
    });
  }

  const processedItems = markDisabledDeep(formData?.items || []);

  return (
    <div className="min-h-screen pb-16 bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Header
              title={isEditMode ? 'Update PO Planning' : 'Create PO Planning'}
              description=""
              infoTitle="Welcomes to Create Planning page"
              infoDesc=""
              paras={[
                'The Planning Page Help use to Create Purchase Planning Seamlessly',
              ]}
            />
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate('/purchase/planning')}
              >
                Back
              </Button>
              <Button
                loading={isCreatePoPlanningLoading || isUpdatePoPlanningLoading}
                onClick={handleSave}
                icon={<SaveOutlined />}
                type="primary"
              >
                Save Planning
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Title level={4} className="mb-4">
          Planning Details
        </Title>
        {/* Form Section */}
        <div className="mb-6 shadow-sm grid grid-cols-2 gap-x-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Planning ID
            </label>
            <IdGenComp {...idCompData} />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Planning Name
              <span className="text-red-500">*</span>
            </label>
            <Input
              size="large"
              placeholder="Enter planning name"
              value={formData?.name}
              onChange={handleChange}
              name="name"
            />
          </div>
        </div>

        <div className="flex justify-self-end gap-x-4">
          <div>
            <Input
              placeholder="Enter base quantity"
              disabled={formData?.items?.length > 0}
              value={formData.baseQuantity}
              min={0}
              type="number"
              onChange={(e) => {
                const value = maxQuantityCheck(e.target.value, 100000000);
                if (!value) return;
                setFormData((prev) => ({
                  ...prev,
                  baseQuantity: e.target.value,
                }));
              }}
              name="baseQuantity"
            />
          </div>
          <Button
            type="primary"
            disabled={
              isEditMode ||
              !formData?.baseQuantity ||
              formData?.baseQuantity <= 0
            }
            onClick={() => {
              if (formData?.baseQuantity <= 0) {
                toast.error('Please enter valid base quantity');
              }
              setOpenBOMSelector(true);
            }}
            icon={<FileTextOutlined />}
          >
            Plan With BOM
          </Button>
          <Button
            type="primary"
            disabled={
              isEditMode ||
              !formData?.baseQuantity ||
              formData?.baseQuantity <= 0
            }
            onClick={() => {
              if (formData?.baseQuantity <= 0) {
                toast.error('Please enter valid base quantity');
              }
              setOpenItemSelector(true);
            }}
            icon={<PlusOutlined />}
          >
            Add Item
          </Button>
        </div>

        {/* Items Table */}
        <div className="shadow-sm">
          <div className="flex justify-between items-center mb-4">
            <Title level={4} className="mb-0">
              Items Planning
            </Title>
            <Text type="secondary">
              {processedItems.length} item
              {processedItems.length !== 1 ? 's' : ''} to plan
            </Text>
          </div>

          <Table
            columns={tableCols}
            dataSource={processedItems}
            rowKey={(_, index) => `item-${index}`}
            pagination={false}
            scroll={{ x: 1200 }}
            size="small"
            className="border border-gray-200 rounded-lg"
            expandable={{
              expandedRowRender: (record, parentRowIndex) => {
                if (record.rawMaterials?.length > 0) {
                  const dataWithParentIndex = record.rawMaterials.map(
                    (rm, rmIndex) => ({
                      ...rm,
                      parentRowIndex,
                      rmIndex,
                    })
                  );
                  return (
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <Title level={5} className="mb-3 text-purple-600">
                        Raw Materials
                      </Title>
                      <Table
                        columns={rmColumns}
                        dataSource={dataWithParentIndex}
                        rowKey={(record) =>
                          `rm-${record.parentRowIndex}-${record.rmIndex}`
                        }
                        pagination={false}
                        size="small"
                        expandable={{
                          expandedRowRender: (record, parentRmIndex) => {
                            if (!record?.selectedVendorsDetails?.length)
                              return null;
                            const dataWithParentIndex =
                              record.selectedVendorsDetails.map(
                                (vd, vdIndex) => ({
                                  ...vd,
                                  parentRmIndex,
                                  vdIndex,
                                  parentRowIndex,
                                  fromRm: true,
                                })
                              );
                            return (
                              <div className="bg-white p-3 rounded border">
                                <Title
                                  level={5}
                                  className="mb-3 text-indigo-600"
                                >
                                  Vendor Details
                                </Title>
                                <Table
                                  columns={vendorColumns}
                                  dataSource={dataWithParentIndex}
                                  rowKey={(record) =>
                                    `vendor-${record.parentRmIndex}-${record.vdIndex}`
                                  }
                                  pagination={false}
                                  size="small"
                                />
                              </div>
                            );
                          },
                          rowExpandable: (record) =>
                            record?.selectedVendorsDetails?.length > 0,
                        }}
                      />
                    </div>
                  );
                } else {
                  return (
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <Title level={5} className="mb-3 text-indigo-600">
                        Vendor Details
                      </Title>
                      <Table
                        columns={vendorColumns}
                        dataSource={record.selectedVendorsDetails?.map(
                          (vd, i) => ({
                            ...vd,
                            parentRowIndex,
                            vdIndex: i,
                            fromRm: false,
                          })
                        )}
                        rowKey={(record) =>
                          `vendor-no-rm-${record.parentRowIndex}-${record.vdIndex}`
                        }
                        pagination={false}
                        size="small"
                      />
                    </div>
                  );
                }
              },
              rowExpandable: (record) =>
                record.rawMaterials?.length > 0 ||
                record?.selectedVendorsDetails?.length > 0,
            }}
          />
        </div>
        {isSaving && <LoadingOverlay />}

        {/* Modals */}
        <BOMSelectorModal
          formData={formData}
          setFormData={setFormData}
          open={openBOMSelector}
          setOpen={setOpenBOMSelector}
        />
        <ExistingPOIndentModal
          open={existingPOIndentModal}
          setOpen={setExistingPOIndentModal}
          data={existingPOIndentData}
        />
        <VendorContactModal
          open={showVendorContactModal}
          setOpen={setShowVendorContactModal}
          data={vendorContactDetails}
        />
        <ItemSelectorModal
          open={openItemSelector}
          setOpen={setOpenItemSelector}
          setFormData={setFormData}
          formData={formData}
        />
      </div>
    </div>
  );
};

export default CreatePOPlanning;
