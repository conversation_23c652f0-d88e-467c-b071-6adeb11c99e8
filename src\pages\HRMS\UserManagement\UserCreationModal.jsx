import { Button, Input, Modal, Steps } from 'antd';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import SelectV2 from '../../../components/global/components/SelectV2';
import {
  useEditUserMutation,
  useGetAllEmployeesQuery,
} from '../../../slices/userApiSlice';
import { customConfirm } from '../../../utils/customConfirm';

const steps = [
  {
    title: 'General',
  },
  {
    title: 'Bank Details',
  },
  {
    title: 'Payroll Details',
  },
];

const UserCreationModal = ({
  openModal,
  setOpenModal,
  isEdit,
  editData,
  setIsEdit,
}) => {
  const [editUser, { isLoading }] = useEditUserMutation();
  const [formData, setFormData] = useState({
    employeeId: '',
    name: '',
    gender: '',
    contactNumber: '',
    email: '',
  });
  const [current, setCurrent] = useState(0);
  const { data: employees = [] } = useGetAllEmployeesQuery();

  useEffect(() => {
    if (formData?.employeeId && !isEdit) {
      let found = employees?.find((elem) => elem?._id === formData?.employeeId);
      setFormData((prev) => ({
        ...prev,
        name: found?.name,
        email: found?.email,
      }));
    }
  }, [formData?.employeeId, isEdit, employees]);

  useEffect(() => {
    if (isEdit && editData?.name !== undefined) {
      let date;
      if (editData?.startDate !== undefined) {
        date = new Date(editData?.startDate)?.toISOString()?.split('T')?.[0];
      }
      setFormData({
        employeeId: editData?._id,
        name: editData?.name,
        gender: editData?.gender || '',
        contactNumber: editData?.contactNumber || '',
        email: editData?.email,
        whatsappNumber: editData?.whatsappNumber || '',
        iifcCode: editData?.iifcCode || '',
        accountNumber: editData?.accountNumber || '',
        bankName: editData?.bankName || '',
        fixedSalary: editData?.fixedSalary || '',
        startDate: date || '',
        workingHours: editData?.workingHours || '',
      });
    }
  }, [isEdit, editData]);

  const closeModal = async (check = true) => {
    if (check) {
      const confirm = await customConfirm(
        'All unsaved progress will be lost. Are you sure you want to close this window?',
        'Delete'
      );
      if (!confirm) return;
    }
    setOpenModal(false);
    setFormData({
      employeeId: '',
      name: '',
      gender: '',
      contactNumber: '',
      email: '',
    });
    setCurrent(0);
    setIsEdit(false);
  };

  const changeHandler = (e) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleSubmit = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to submit this information?',
      'Success'
    );
    if (!confirm) return;
    let employeeId = formData?.employeeId;
    delete formData.employeeId;
    let data = {
      ...formData,
      isHrms: true,
      attendance: editData?.attendance || {},
    };
    const res = await editUser({ data: data, id: employeeId });
    if (res?.data) {
      toast.success('User succesfully edited');
      closeModal(false);
    }
  };

  const renderContent = () => {
    switch (current) {
      case 0:
        return (
          <div className="grid grid-cols-2 gap-4 items-center">
            <div>
              <p>Import Employee</p>
              <SelectV2
                name="employeeId"
                options={
                  !isEdit
                    ? employees
                        ?.filter((elem) => !elem?.isHrms)
                        ?.map((elem) => ({
                          label: elem?.name,
                          value: elem?._id,
                        }))
                    : employees?.map((elem) => ({
                        label: elem?.name,
                        value: elem?._id,
                      }))
                }
                value={formData?.employeeId}
                onChange={changeHandler}
                disabled={isEdit}
              />
            </div>
            <div>
              <p>Name</p>
              <Input
                placeholder="Enter Name"
                name="name"
                onChange={changeHandler}
                value={formData?.name}
                disabled={
                  formData?.employeeId === undefined ||
                  formData?.employeeId?.length === 0
                }
              />
            </div>
            <div>
              <p>Gender</p>
              <Input
                placeholder="Enter Gender"
                name="gender"
                onChange={changeHandler}
                value={formData?.gender}
                disabled={
                  formData?.employeeId === undefined ||
                  formData?.employeeId?.length === 0
                }
              />
            </div>
            <div>
              <p>Email</p>
              <Input
                placeholder="Enter Email"
                name="email"
                onChange={changeHandler}
                value={formData?.email}
                disabled={
                  formData?.employeeId === undefined ||
                  formData?.employeeId?.length === 0
                }
              />
            </div>
            <div>
              <p>Contact Number</p>
              <Input
                placeholder="Enter Contact Number"
                name="contactNumber"
                onChange={changeHandler}
                value={formData?.contactNumber}
                disabled={
                  formData?.employeeId === undefined ||
                  formData?.employeeId?.length === 0
                }
              />
            </div>
            <div>
              <p>Whatsapp Number</p>
              <Input
                placeholder="Enter Whatsapp Number"
                name="whatsappNumber"
                onChange={changeHandler}
                value={formData?.whatsappNumber}
                disabled={
                  formData?.employeeId === undefined ||
                  formData?.employeeId?.length === 0
                }
              />
            </div>
          </div>
        );
      case 1:
        return (
          <div className="grid grid-cols-2 items-center gap-4">
            <div>
              <p>IIFC Number</p>
              <Input
                placeholder="Enter IIFC Number"
                name="iifcCode"
                onChange={changeHandler}
                value={formData?.iifcCode}
              />
            </div>
            <div>
              <p>Account Number</p>
              <Input
                placeholder="Enter Account Number"
                name="accountNumber"
                onChange={changeHandler}
                value={formData?.accountNumber}
              />
            </div>
            <div>
              <p>Bank Name</p>
              <Input
                placeholder="Enter Bank Name"
                name="bankName"
                onChange={changeHandler}
                value={formData?.bankName}
              />
            </div>
          </div>
        );
      case 2:
        return (
          <div className="grid grid-cols-2 items-center gap-4">
            <div>
              <p>Fixed Salary</p>
              <Input
                type="Number"
                placeholder="Enter Fixed Salary"
                name="fixedSalary"
                onChange={changeHandler}
                value={formData?.fixedSalary}
              />
            </div>
            <div>
              <p>Start Date</p>
              <Input
                type="date"
                placeholder="Enter Start Date"
                name="startDate"
                onChange={changeHandler}
                value={formData?.startDate}
              />
            </div>
            <div>
              <p>Working Hours (per Day)</p>
              <Input
                type="Number"
                placeholder="Enter Working Hours"
                name="workingHours"
                onChange={changeHandler}
                value={formData?.workingHours}
              />
            </div>
          </div>
        );
    }
  };

  return (
    <>
      <Modal
        title="Add User"
        open={openModal}
        onCancel={closeModal}
        width={1300}
        footer={[
          <Button
            key="back"
            onClick={() => {
              if (current === 0) {
                closeModal();
              } else {
                setCurrent((prev) => prev - 1);
              }
            }}
          >
            {current === 0 ? 'Cancel' : 'Previous'}
          </Button>,
          <Button
            key="submit"
            type="primary"
            disabled={
              formData?.employeeId === undefined ||
              formData?.employeeId?.length === 0
            }
            loading={isLoading}
            onClick={() => {
              if (current === steps?.length - 1) {
                handleSubmit();
              } else {
                setCurrent((prev) => prev + 1);
              }
            }}
          >
            {current === steps?.length - 1 ? 'Submit' : 'Next'}
          </Button>,
        ]}
      >
        <Steps size="small" current={current} className="mt-4" items={steps} />
        <div className="w-full mt-4 h-[30vh]">{renderContent()}</div>
      </Modal>
    </>
  );
};

export default UserCreationModal;
