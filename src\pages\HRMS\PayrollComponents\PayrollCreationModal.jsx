import { useState } from 'react';
import { toast } from 'react-toastify';

import { Modal } from 'antd';
import { useCreatePayrollMutation } from '../../../slices/HRMS/payrollApiSlice';
import { useGetUsersForHrmsQuery } from '../../../slices/userApiSlice';

import Input from '../../../components/global/components/Input';
import MultiSelect from '../../../components/global/components/MultiSelect';
import { customConfirm } from '../../../utils/customConfirm';

const PayrollCreationModal = ({ openModal, setOpenModal }) => {
  const { data: users } = useGetUsersForHrmsQuery();
  const [createPayroll, { isLoading }] = useCreatePayrollMutation();

  const [formData, setFormData] = useState({
    startDate: '',
    endDate: '',
    users: [],
  });

  const changeHandler = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const closeModal = async (check = true) => {
    if (check) {
      const confirm = await customConfirm(
        'All unsaved progress will be lost. Are you sure you want to close this window?',
        'Delete'
      );
      if (!confirm) return;
    }
    setOpenModal(false);
    setFormData({
      startDate: '',
      endDate: '',
      users: [],
    });
  };

  const handleSubmit = async () => {
    const confirm = await customConfirm(
      'Submit the current information?',
      'Success'
    );
    if (!confirm) return;
    const res = await createPayroll({
      data: {
        ...formData,
        users: formData?.users?.map((elem) => elem?.value),
      },
    });
    if (res?.data) {
      toast.success('Payroll Created');
      closeModal(false);
    } else {
      toast.error(`Error: ${res?.error?.message}`);
    }
  };

  return (
    <Modal
      title="Create Payroll"
      open={openModal}
      onOk={handleSubmit}
      onCancel={closeModal}
      width={900}
      confirmLoading={isLoading}
    >
      <div>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <p className="block text-sm font-medium text-gray-700">
              Start Date
            </p>
            <Input
              name="startDate"
              value={formData?.startDate}
              onChange={(e) => {
                changeHandler(e);
              }}
              type="date"
            />
          </div>
          <div>
            <p className="block text-sm font-medium text-gray-700">End Date</p>
            <Input
              name="endDate"
              value={formData?.endDate}
              onChange={(e) => {
                changeHandler(e);
              }}
              type="date"
            />
          </div>
        </div>
        <div className="mt-2">
          <p className="block text-sm font-medium text-gray-700">
            Assign Users
          </p>
          <MultiSelect
            name="users"
            value={formData?.users}
            onChange={(e) => {
              changeHandler(e);
            }}
            options={users?.map((elem) => ({
              label: elem?.name,
              value: elem?._id,
            }))}
          />
        </div>
      </div>
    </Modal>
  );
};

export default PayrollCreationModal;
