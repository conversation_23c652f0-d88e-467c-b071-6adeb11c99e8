import { PlusIcon } from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import {
  useCreateDashboardTemplateMutation,
  useDeleteDashboardTemplateMutation,
  useEditDashboardTemplateMutation,
  useLazyGetTemplatePagesQuery,
} from '../../slices/dsahboardTemplateApiSlice';
import { useGetPrefixIdQuery } from '../../slices/prefixIdApiSlice';
import {
  DEFAULT_LEADS_HEADER,
  DEFAULT_PO_HEADER,
  DEFAULT_QUOTATION_HEADER,
} from '../../utils/Constant';
import AddButton from '../AddButton';
import Button from '../global/components/Button';
import { default as Header } from '../global/components/Header';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import Tooltip from '../global/components/ToolTip';

const CreateTemplate = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [format, setFormat] = useState({});

  const templatePageOptions = [
    {
      value: '/salesordermanagement/salesinquirydashboard',
      name: 'Sales Inquiry',
      id: 'salesInquiryId',
    },
    {
      value: '/salesordermanagement/quotation',
      name: 'Quotation',
      id: 'quotationId',
    },
    {
      value: '/salesordermanagement/orders',
      name: 'Sales Order',
      id: 'salesOrderId',
    },
    {
      value: '/jobs/workorder',
      name: 'Work Order',
      id: 'workOrderId',
    },
    {
      value: '/jobs/outsource',
      name: 'Outsource Jobs',
      id: 'outsourceId',
    },
    {
      value: '/dispatch/dashboard',
      name: 'Dispatch',
      id: 'dispatchId',
    },
    {
      value: 'inventory/outpage/stockout',
      name: 'Stock Out',
    },
    {
      value: '/settings/config/customer',
      name: 'Customer',
      id: 'customerId',
    },
    {
      value: '/settings/inventory/products/manage',
      name: 'Products',
      id: 'productId',
    },
    {
      value: '/jobs/ncr/create',
      name: 'NCR',
      id: 'ncrId',
    },
    {
      value: '/purchase/po',
      name: 'Purchase Order',
      id: 'poId',
    },
    {
      value: '/crm/leads',
      name: 'Leads',
    },
    {
      value: '/inventory/inpage/grn',
      name: 'Stock In',
    },
    {
      value: '/renewalsmanagement/renewals',
      name: 'Renewals',
      id: 'renewalId',
    },
    {
      value: '/primary/tasks',
      name: 'Daily Tasks',
    },
    {
      value: '/settings/inventory/assets/manage',
      name: 'Asset',
      id: 'assetsId',
    },
    {
      value: '/accountmanagement/voucher/receiptVoucher',
      name: 'Receipt Voucher',
      id: 'receiptVoucherId',
    },
    {
      value: '/accountmanagement/voucher/journalVoucher',
      name: 'Journal Voucher',
      id: 'journalVoucherId',
    },
    {
      value: '/accountmanagement/voucher/purchaseVoucher',
      name: 'Purchase Voucher',
      id: 'purchaseVoucherId',
    },
    {
      value: '/accountmanagement/voucher/salesVoucher',
      name: 'Sales Voucher',
      id: 'salesVoucherId',
    },
    {
      value: '/accountmanagement/voucher/paymentVoucher',
      name: 'Payment Voucher',
      id: 'paymentVoucherId',
    },
    {
      value: '/accountmanagement/invoice/salesInvoice',
      name: 'Sales Invoice',
      id: 'salesInvoiceId',
    },
    {
      value: '/accountmanagement/invoice/purchaseInvoice',
      name: 'Purchase Invoice',
      id: 'purchaseInvoiceId',
    },

    {
      value: '/accountmanagement/bill/payableBill',
      name: 'Payable Bill',
      id: 'payableBillId',
    },
    {
      value: '/accountmanagement/bill/receivableBill',
      name: 'Receivable Bill',
      id: 'receivableBillId',
    },
  ];
  const [fieldName, setFieldName] = useState('');
  const [fieldType, setFieldType] = useState('');
  const [fieldOptions, setFieldOptions] = useState([]);
  const [createTemplate] = useCreateDashboardTemplateMutation();
  const [editTemplate] = useEditDashboardTemplateMutation();
  const [deleteTemplate] = useDeleteDashboardTemplateMutation();
  const [getTemplates, { data: templates }] = useLazyGetTemplatePagesQuery();
  const [isAdd, setIsAdd] = useState(true);

  useEffect(() => {
    getTemplates({});
  }, [getTemplates]);

  const [templateData, setTemplateData] = useState({
    name: '',
    cslug: '',
    templateData: [],
    idFormat: {},
    idIndex: -1,
  });
  const { data: prefixIds } = useGetPrefixIdQuery();

  const toastObject = (options, pageName) => {
    const val = options?.map((el) => el.headerName?.toLowerCase()?.trim());
    let errorMsg = '';

    // Check if the fieldName already exists in options (headerName)
    if (val.includes(fieldName?.toLowerCase()?.trim())) {
      errorMsg += `${fieldName} Field Already Exists in ${pageName}. `;
    }

    // Check if the fieldName already exists in templateData
    const match = templateData?.templateData?.find(
      (item) =>
        item?.fieldName?.toLowerCase()?.trim() ===
        fieldName?.toLowerCase()?.trim()
    );

    if (match) {
      errorMsg += `${fieldName} Field Already Exists In Template Data.`;
    }

    // If there's an error message, show toast and return true
    if (errorMsg.length > 0) {
      toast.error(errorMsg);
      return true;
    }

    // No error, return false
    return false;
  };

  const handleAdd = () => {
    if (fieldName === '' || fieldType === '') {
      toast.error('Fieldname And Fieldtype Is Required');
      return;
    }

    let currentslug = templateData.cslug;

    if (currentslug === '/purchase/po') {
      if (toastObject(DEFAULT_PO_HEADER, 'Purchase Order Table')) return;
    } else if (currentslug === '/salesordermanagement/quotation') {
      if (toastObject(DEFAULT_QUOTATION_HEADER, 'Quotation Table')) return;
    } else if (currentslug === '/crm/leads') {
      if (toastObject(DEFAULT_LEADS_HEADER, 'Leads Table')) return;
    }

    setTemplateData((prev) => {
      return {
        ...prev,
        templateData: [
          ...prev.templateData,
          {
            fieldName,
            fieldType,
            fieldOptions: fieldOptions?.map((options) => ({
              value: options,
              label: options,
            })),
          },
        ],
      };
    });
    setFieldName('');
    setFieldType('');
    setFieldOptions([]);
  };

  const handleSubmit = async () => {
    if (templateData.name === '' || templateData.cslug === '') {
      toast.error('Name and department page is required');
    }
    // Removing this Check
    // if (templateData.templateData.length === 0) {
    //   toast.error('Need to atleast one field to template');
    // }
    if (Object.keys(templateData?.idFormat).length === 0) {
      toast.error('Please provide ID Format');
      return;
    }
    if (isAdd) {
      const response = await createTemplate({
        data: {
          ...templateData,
          prefixId:
            templatePageOptions?.find((i) => i?.value === templateData?.cslug)
              ?.id || '',
        },
        path: templateData.cslug,
      });
      if (!response.error) {
        toast.success('Template added Successfully');
        setTemplateData({
          name: '',
          cslug: '',
          templateData: [],
          idFormat: {},
          idIndex: -1,
        });
        setFieldName('');
        setFieldType('');
        setModalOpen(false);
      }
    } else {
      const response = await editTemplate({
        data: templateData,
        id: templateData._id,
      });
      if (response) {
        toast.success('Template edited Successfully');
        setModalOpen(false);
      }
      setTemplateData({
        name: '',
        cslug: '',
        templateData: [],
        idFormat: {},
        idIndex: -1,
      });
      setFieldName('');
      setFieldType('');
    }
  };

  useEffect(() => {
    if (prefixIds) {
      if (
        templateData?.cslug === '/salesordermanagement/salesinquirydashboard'
      ) {
        setFormat(prefixIds?.salesInquiryId);
      }
      if (templateData?.cslug === '/salesordermanagement/orders') {
        setFormat(prefixIds?.salesOrderId);
      }
      if (templateData?.cslug === '/salesordermanagement/quotation') {
        setFormat(prefixIds?.quotationId);
      }
      if (templateData?.cslug === '/jobs/workorder') {
        setFormat(prefixIds?.workOrderId);
      }
      if (templateData?.cslug === '/jobs/outsource') {
        setFormat(prefixIds?.outsourceId);
      }
      if (templateData?.cslug === '/dispatch/dashboard') {
        setFormat(prefixIds?.dispatchId);
      }
      if (templateData?.cslug === 'inventory/outpage/stockout') {
        setFormat(prefixIds?.chalanId);
      }
      if (templateData?.cslug === '/settings/config/customer') {
        setFormat(prefixIds?.customerId);
      }
      if (templateData?.cslug === '/jobs/ncr/create') {
        setFormat(prefixIds?.ncrId);
      }

      if (templateData?.cslug === '/purchase/po') {
        setFormat(prefixIds?.poId);
      }
      if (templateData?.cslug === '/crm/leads') {
        setFormat(prefixIds?.leadId);
      }
      if (templateData?.cslug === '/inventory/inpage/grn') {
        setFormat(prefixIds?.grnId);
      }
      if (templateData?.cslug === '/renewalsmanagement/renewals') {
        setFormat(prefixIds?.renewalId);
      }
      if (templateData?.cslug === '/primary/tasks') {
        setFormat(prefixIds?.dailyTaskId);
      }
      if (templateData?.cslug === '/dispatch/dashboard') {
        setFormat(prefixIds?.dispatchId);
      }
      if (templateData?.cslug === '/settings/inventory/products/manage') {
        setFormat(prefixIds?.productId);
      }
      if (templateData?.cslug === '/settings/inventory/assets/manage') {
        setFormat(prefixIds?.assetsId);
      }
      if (templateData?.cslug === '/accountmanagement/voucher/receiptVoucher') {
        setFormat(prefixIds?.receiptVoucherId);
      }
      if (templateData?.cslug === '/accountmanagement/voucher/journalVoucher') {
        setFormat(prefixIds?.journalVoucherId);
      }
      if (
        templateData?.cslug === '/accountmanagement/voucher/purchaseVoucher'
      ) {
        setFormat(prefixIds?.purchaseVoucherId);
      }
      if (templateData?.cslug === '/accountmanagement/voucher/salesVoucher') {
        setFormat(prefixIds?.salesVoucherId);
      }
      if (templateData?.cslug === '/accountmanagement/voucher/paymentVoucher') {
        setFormat(prefixIds?.paymentVoucherId);
      }
    }
  }, [templateData?.cslug, prefixIds]);

  const getIdOptions = () => {
    let formatArray = [];
    if (Array.isArray(format)) {
      format?.forEach((idFormat) => {
        let transformedIdFormat = '';
        for (let fieldType of Object.keys(idFormat).filter(
          (item) => item !== 'isUsed'
        )) {
          let type = fieldType.substring(0, fieldType?.indexOf('_'));
          if (type === 'String' || type === 'Increment') {
            transformedIdFormat = transformedIdFormat.concat(
              `${idFormat?.[fieldType]}`
            );
          } else {
            transformedIdFormat = transformedIdFormat.concat(`${type}`);
          }
        }
        formatArray.push({
          label: transformedIdFormat,
          value: idFormat,
        });
      });
    } else {
      let transformedIdFormat = '';
      for (let fieldType of Object.keys(format).filter(
        (item) => item !== 'isUsed'
      )) {
        let type = fieldType.substring(0, fieldType?.indexOf('_'));
        if (type === 'String' || type === 'Increment') {
          transformedIdFormat = transformedIdFormat.concat(
            `${format?.[fieldType]}`
          );
        } else {
          transformedIdFormat = transformedIdFormat.concat(`${type}`);
        }
      }
      formatArray.push({
        label: transformedIdFormat,
        value: format,
      });
    }
    return formatArray;
  };

  const handleDelete = async (template) => {
    await deleteTemplate({ id: template._id }).then(() =>
      toast.success('Template deleted')
    );
  };

  const handleEdit = (template) => {
    setModalOpen(true);
    setTemplateData(template);
    setIsAdd(false);
  };

  useEffect(() => {
    if (!isAdd) {
      setTemplateData((prev) => {
        return {
          ...prev,
          idFormat: format?.[templateData?.idIndex],
        };
      });
    }
  }, [format, isAdd, templateData?.idIndex]);

  const handleCopy = (template) => {
    setModalOpen(true);
    setTemplateData({
      name: template?.name,
      cslug: template?.cslug,
      templateData: template?.templateData,
      idIndex: template?.idIndex,
      idFormat: format?.[template?.idIndex],
    });
    setIsAdd(true);
  };

  const handleColumnNameChange = (value, index, type, fieldWithTableIndex) => {
    setTemplateData((prev) => {
      const updatedTemplateData = [...prev.templateData];
      const updatedTableOptions = {
        ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
      };

      if (!updatedTableOptions.column) {
        updatedTableOptions.column = [];
      } else {
        updatedTableOptions.column = [...updatedTableOptions.column];
      }
      if (!updatedTableOptions.column[index]) {
        updatedTableOptions.column[index] = {
          columnName: '',
          columnType: '',
          dropdownOptions: [],
        };
      }
      if (type === 'name') {
        updatedTableOptions.column[index] = {
          ...updatedTableOptions.column[index],
          columnName: value,
        };
      } else if (type === 'type') {
        updatedTableOptions.column[index] = {
          ...updatedTableOptions.column[index],
          columnType: value,
        };
      }

      updatedTemplateData[fieldWithTableIndex] = {
        ...updatedTemplateData[fieldWithTableIndex],
        tableOptions: updatedTableOptions,
      };

      return {
        ...prev,
        templateData: updatedTemplateData,
      };
    });
  };

  const handleColumnDropdownOptionsAdd = (
    value,
    index,
    dropdownIndex,
    fieldWithTableIndex
  ) => {
    setTemplateData((prev) => {
      const updatedTemplateData = [...prev.templateData];
      const updatedTableOptions = {
        ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
      };

      if (!updatedTableOptions.column) {
        updatedTableOptions.column = [];
      } else {
        updatedTableOptions.column = [...updatedTableOptions.column];
      }

      if (!updatedTableOptions.column[index]) {
        updatedTableOptions.column[index] = {
          columnName: '',
          columnType: '',
          dropdownOptions: [],
        };
      }

      const updatedDropdownOptions = [
        ...updatedTableOptions.column[index].dropdownOptions,
      ];
      updatedDropdownOptions[dropdownIndex] = value;

      updatedTableOptions.column[index] = {
        ...updatedTableOptions.column[index],
        dropdownOptions: updatedDropdownOptions,
      };

      updatedTemplateData[fieldWithTableIndex] = {
        ...updatedTemplateData[fieldWithTableIndex],
        tableOptions: updatedTableOptions,
      };

      return {
        ...prev,
        templateData: updatedTemplateData,
      };
    });
  };

  return (
    <div>
      <div className="w-full flex flex-row justify-between gap-2">
        <div className="flex flex-col">
          <div className="flex items-center gap-[5px]">
            <Header
              title="Template"
              description=""
              infoTitle="Welcome to the Template page"
              infoDesc="Your production blueprint control center. Here, you can effortlessly construct your entire production
                flow, encompassing in-house, outsourced, and quality control
                processes. Planned downtimes are intuitively assigned to the
                respective processes, ensuring an optimized workflow. Plus, you
                have the flexibility to set default process goal values for
                seamless production management."
              paras={[
                'Welcome to the Template Creation feature, where you can add a new template for form design. With this tool, you can create custom form templates to streamline data collection, organize information, and improve efficiency in your workflows.',
              ]}
            />
          </div>
          <p className="text-[#676464] text-sm"></p>
        </div>
      </div>
      {modalOpen && (
        <Modal
          title={'Add a new template'}
          onCloseModal={() => {
            setModalOpen(false);
            setTemplateData({
              name: '',
              cslug: '',
              templateData: [],
              idFormat: {},
              idIndex: -1,
            });
            setFieldName('');
            setFieldType('');
            setIsAdd(true);
          }}
          onAdd={{ label: 'Add', func: [handleAdd], step: [0] }}
          onSubmit={() => handleSubmit()}
          pages={
            templateData?.templateData?.some(
              (template) => template.fieldType === 'table'
            )
              ? [
                  'Form Design',
                  ...Array.from({
                    length: templateData?.templateData?.filter(
                      (template) => template.fieldType === 'table'
                    ).length,
                  }).map((_, i) => `Table ${i + 1}`),
                ]
              : ['Form Design']
          }
        >
          {({ step: tempStep }) => {
            const templateStep = tempStep + 1;

            const tableTemplates = templateData.templateData.filter(
              (template) => template.fieldType === 'table'
            );
            const fieldWithTableIndex = templateData.templateData.findIndex(
              (template) => template === tableTemplates[templateStep - 2]
            );

            return templateStep === 1 ? (
              <div>
                <div>
                  <label>Template Name</label>{' '}
                  <span className="text-xl text-red-500 -mt-1 -ml-1">*</span>
                  <Input
                    value={templateData.name}
                    onChange={(e) =>
                      setTemplateData((prev) => {
                        return {
                          ...prev,
                          name: e.target.value,
                        };
                      })
                    }
                    disabled={!isAdd}
                    placeholder="Enter template name"
                    required="true"
                  />
                </div>
                <div>
                  <label>Choose Page</label>
                  <span className="text-xl text-red-500 -mt-1">*</span>

                  <Select
                    options={templatePageOptions}
                    value={templateData.cslug}
                    onChange={(e) => {
                      const value = e.target.value;

                      setTemplateData((prev) => ({
                        ...prev,
                        cslug: value,
                      }));
                    }}
                    disabled={!isAdd}
                  />
                </div>
                {format && JSON.stringify(format) !== '{}' && (
                  <div>
                    <label>Choose ID Format</label>
                    <span className="text-xl text-red-500 -mt-1">*</span>

                    <Select
                      options={getIdOptions()}
                      menuPlacement={'top'}
                      value={templateData.idFormat}
                      disabled={!isAdd}
                      onChange={(e) => {
                        setTemplateData((prev) => {
                          return {
                            ...prev,
                            idFormat: e.target.value,
                          };
                        });
                        let selectedIndex = -1;
                        for (let i in format) {
                          if (
                            JSON.stringify(format[i]) ===
                            JSON.stringify(e.target.value)
                          ) {
                            selectedIndex = parseInt(i);
                            break;
                          }
                        }
                        setTemplateData((prev) => {
                          return {
                            ...prev,
                            idIndex: selectedIndex,
                          };
                        });
                      }}
                    />
                  </div>
                )}
                <div>
                  <label>Enter Field Name</label>{' '}
                  <span className="text-xl text-red-500 -mt-1 -ml-1">*</span>
                  <Input
                    value={fieldName}
                    onChange={(e) => setFieldName(e.target.value)}
                  />
                </div>
                <div>
                  <label>Enter Field Type</label>{' '}
                  <span className="text-xl text-red-500 -mt-1 -ml-1">*</span>
                  <Select
                    value={fieldType}
                    menuPlacement="top"
                    options={[
                      { value: 'string', label: 'String' },
                      { value: 'number', label: 'Number' },
                      { value: 'Date', label: 'Date' },
                      { value: 'Check', label: 'Checkbox' },
                      { value: 'Dropdown', label: 'Dropdown' },
                      { value: 'table', label: 'Table' },
                      { value: 'Description', label: 'Description' },
                      { value: 'Media', label: 'Media' },
                      {
                        value: 'MultiSelect',
                        label: 'MultiSelect',
                      },
                    ]}
                    onChange={(e) => {
                      setFieldType(e.target.value);
                    }}
                  />
                </div>
                {(fieldType === 'Dropdown' || fieldType === 'MultiSelect') && (
                  <>
                    <div className="flex gap-5 mt-3">
                      <label>Enter dropdown options</label>
                      <PlusIcon
                        onClick={() => setFieldOptions((prev) => [...prev, ''])}
                        width={20}
                        height={20}
                        className="text-white bg-blue-400 rounded-full cursor-pointer"
                      />
                    </div>
                    <div className="w-1/2 mt-1">
                      {fieldOptions?.map((options, index) => (
                        <Input
                          key={index}
                          onChange={(e) => {
                            const updated = [...fieldOptions];
                            updated[index] = e.target.value;
                            setFieldOptions(updated);
                          }}
                          placeholder="Enter option"
                          value={options}
                          className={`mb-1`}
                        />
                      ))}
                    </div>
                  </>
                )}

                {templateData.templateData.length > 0 && (
                  <Table className="mt-4">
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>Fieldname</Table.Th>
                        <Table.Th>FieldType</Table.Th>
                        <Table.Th>ADDITIONAL CHARGES</Table.Th>
                        <Table.Th>Actions</Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {templateData.templateData.map((item, idx) => {
                        return (
                          <Table.Row key={idx}>
                            <Table.Td>{item.fieldName}</Table.Td>
                            <Table.Td>{item.fieldType}</Table.Td>
                            <Table.Td>
                              <input
                                type="checkbox"
                                className="w-3 h-3 cursor-pointer"
                                disabled={item.fieldType !== 'number'}
                                value={item?.isAdditionalCharged}
                                onClick={(e) => {
                                  setTemplateData((prev) => ({
                                    ...prev,
                                    templateData: prev?.templateData?.map(
                                      (el, i) => {
                                        if (
                                          i === idx &&
                                          el.fieldType === 'number'
                                        ) {
                                          return {
                                            ...el,
                                            isAdditionalCharged:
                                              e.target.checked,
                                          };
                                        }

                                        return el;
                                      }
                                    ),
                                  }));
                                }}
                              />
                            </Table.Td>
                            <Table.Td
                              onClick={() => {
                                setTemplateData((prev) => {
                                  const updatedTemplateData = [
                                    ...prev.templateData,
                                  ];
                                  updatedTemplateData.splice(idx, 1);
                                  return {
                                    ...prev,
                                    templateData: updatedTemplateData,
                                  };
                                });
                              }}
                              className={`text-red-500 underline cursor-pointer`}
                            >
                              Remove
                            </Table.Td>
                          </Table.Row>
                        );
                      })}
                    </Table.Body>
                  </Table>
                )}
              </div>
            ) : (
              <div>
                <div className="flex items-center gap-3 mb-3">
                  <span className="flex items-center gap-1">
                    <label>Enter number of columns: </label>
                    <Input
                      value={
                        templateData.templateData[fieldWithTableIndex]
                          ?.tableOptions?.columns || 0
                      }
                      type="number"
                      onChange={(e) => {
                        const newValue =
                          +e.target.value > 9 ? 9 : +e.target.value;
                        if (+e.target.value > 9) {
                          toast.warning('Can add upto 9 columns');
                        }
                        setTemplateData((prev) => {
                          const updatedTemplateData = [...prev.templateData];
                          updatedTemplateData[fieldWithTableIndex] = {
                            ...updatedTemplateData[fieldWithTableIndex],
                            tableOptions: {
                              ...updatedTemplateData[fieldWithTableIndex][
                                'tableOptions'
                              ],
                              columns: newValue,
                            },
                          };
                          return {
                            ...prev,
                            templateData: updatedTemplateData,
                          };
                        });
                      }}
                    />
                  </span>
                  <span className="flex items-center gap-1">
                    <label>Enter number of rows: </label>
                    <Input
                      value={
                        templateData.templateData[fieldWithTableIndex]
                          ?.tableOptions?.rows || 0
                      }
                      type="number"
                      onChange={(e) => {
                        const newValue = +e.target.value;
                        setTemplateData((prev) => {
                          const updatedTemplateData = [...prev.templateData];
                          updatedTemplateData[fieldWithTableIndex] = {
                            ...updatedTemplateData[fieldWithTableIndex],
                            tableOptions: {
                              ...updatedTemplateData[fieldWithTableIndex][
                                'tableOptions'
                              ],
                              rows: newValue,
                            },
                          };
                          return {
                            ...prev,
                            templateData: updatedTemplateData,
                          };
                        });
                      }}
                    />
                  </span>
                </div>
                <hr />
                <div className="overflow-x-scroll pb-16 mb-3">
                  {Array.from({
                    length:
                      templateData.templateData[fieldWithTableIndex]
                        ?.tableOptions?.columns,
                  }).map((_, index) => (
                    <div key={index}>
                      <label className="block mb-1 text-lg text-gray-800 font-medium">
                        Column {index + 1}:
                      </label>
                      <div className="w-full flex pb-3">
                        <span className="w-1/2 flex items-center gap-1">
                          <label className="block mb-1 text-sm text-gray-500 font-medium">
                            Column Name:{' '}
                          </label>
                          <Input
                            onChange={(e) => {
                              const value = e.target.value;
                              handleColumnNameChange(
                                value,
                                index,
                                'name',
                                fieldWithTableIndex
                              );
                            }}
                            value={
                              templateData?.templateData?.[fieldWithTableIndex]
                                ?.tableOptions?.column?.[index]?.columnName ||
                              ''
                            }
                            className={`!w-fit`}
                          />
                        </span>
                        <span className="w-1/2 flex items-center gap-1 justify-start">
                          <label className="block mb-1 text-sm text-gray-500 font-medium">
                            Column Type:{' '}
                          </label>
                          <Select
                            onChange={(e) => {
                              const value = e.target.value;
                              handleColumnNameChange(
                                value,
                                index,
                                'type',
                                fieldWithTableIndex
                              );
                            }}
                            menuPortalTarget={document.body}
                            options={[
                              { value: 'date', label: 'Date' },
                              { value: 'number', label: 'Number' },
                              { value: 'string', label: 'String' },
                              { value: 'dropdown', label: 'Dropdown' },
                            ]}
                            value={
                              templateData?.templateData?.[fieldWithTableIndex]
                                ?.tableOptions?.column?.[index]?.columnType ||
                              ''
                            }
                            className={`!w-1/2`}
                            styles={{
                              menuPortal: (base) => ({ ...base, zIndex: 9999 }),
                            }}
                          />
                        </span>
                      </div>
                      {templateData?.templateData?.[fieldWithTableIndex]
                        ?.tableOptions?.column?.[index]?.columnType ===
                        'dropdown' && (
                        <div className="mb-3">
                          <Button
                            onClick={() =>
                              handleColumnDropdownOptionsAdd(
                                '',
                                index,
                                templateData?.templateData?.[
                                  fieldWithTableIndex
                                ]?.tableOptions?.column?.[index]
                                  ?.dropdownOptions?.length,
                                fieldWithTableIndex
                              )
                            }
                            className={`mb-3`}
                          >
                            + Add Options
                          </Button>
                          <div className="flex items-center w-fit">
                            {templateData?.templateData?.[
                              fieldWithTableIndex
                            ]?.tableOptions?.column?.[
                              index
                            ]?.dropdownOptions?.map((options, idx) => (
                              <Input
                                key={idx}
                                value={options}
                                onChange={(e) =>
                                  handleColumnDropdownOptionsAdd(
                                    e.target.value,
                                    index,
                                    idx,
                                    fieldWithTableIndex
                                  )
                                }
                              />
                            ))}
                          </div>
                        </div>
                      )}
                      <hr />
                    </div>
                  ))}
                </div>
                <div>
                  <label>Table Preview:</label>
                  <div className="overflow-x-scroll">
                    <Table>
                      <Table.Head>
                        <Table.Row>
                          {templateData?.templateData?.[
                            fieldWithTableIndex
                          ]?.tableOptions?.column?.map((col, idx) => (
                            <Table.Th key={idx}>{col?.columnName}</Table.Th>
                          ))}
                        </Table.Row>
                      </Table.Head>
                      <Table.Body>
                        <Table.Row>
                          {templateData?.templateData?.[
                            fieldWithTableIndex
                          ]?.tableOptions?.column?.map((col, idx) => {
                            if (col?.columnType === 'dropdown') {
                              return (
                                <Table.Td key={idx}>
                                  <Select
                                    options={col?.dropdownOptions?.map(
                                      (item) => ({ value: item, label: item })
                                    )}
                                  />
                                </Table.Td>
                              );
                            } else if (col?.columnType === 'string') {
                              return (
                                <Table.Td key={idx}>
                                  <Input type="string" value={''} />
                                </Table.Td>
                              );
                            } else if (col?.columnType === 'number') {
                              return (
                                <Table.Td key={idx}>
                                  <Input type="number" value={0} />
                                </Table.Td>
                              );
                            } else if (col?.columnType === 'date') {
                              return (
                                <Table.Td key={idx}>
                                  <Input type="date" />
                                </Table.Td>
                              );
                            } else if (col?.columnType === 'checkbox') {
                              return (
                                <Table.Td key={idx}>
                                  <Input type="checkbox" />
                                </Table.Td>
                              );
                            } else {
                              return (
                                <Table.Td key={idx}>
                                  <span>Unknown Type</span>
                                </Table.Td>
                              );
                            }
                          })}
                        </Table.Row>
                      </Table.Body>
                    </Table>
                  </div>
                </div>
                {templateData?.templateData[fieldWithTableIndex]?.tableOptions
                  ?.rows > 0 && (
                  <div className="w-full">
                    <label>Enter Row Names: </label>
                    <div className="flex gap-3 flex-wrap w-full">
                      {Array.from({
                        length:
                          templateData?.templateData[fieldWithTableIndex]
                            ?.tableOptions?.rows,
                      }).map((_, idx) => {
                        return (
                          <div key={idx}>
                            <label>Row {idx + 1}:</label>
                            <Input
                              key={idx}
                              onChange={(e) => {
                                const updatedRowNames = [
                                  ...(templateData?.templateData[
                                    fieldWithTableIndex
                                  ]?.tableOptions?.rowNames || []),
                                ];
                                updatedRowNames[idx] = e.target.value;
                                setTemplateData((prev) => {
                                  return {
                                    ...prev,
                                    templateData: prev.templateData.map(
                                      (item, itemIndex) => {
                                        if (itemIndex === fieldWithTableIndex) {
                                          return {
                                            ...item,
                                            tableOptions: {
                                              ...item.tableOptions,
                                              rowNames: updatedRowNames,
                                            },
                                          };
                                        }
                                        return item;
                                      }
                                    ),
                                  };
                                });
                              }}
                            />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            );
          }}
        </Modal>
      )}
      <div className="flex justify-end items-center w-full bg-white rounded-tl-lg rounded-tr-lg mt-2 p-2">
        <div className="flex items-center">
          <AddButton
            className="!h-6 !text-xs !w-[5rem]"
            modalOpen={modalOpen}
            setModalOpen={setModalOpen}
          />
        </div>
      </div>

      <div>
        <Table>
          <Table.Head>
            <Table.Row>
              <Table.Th>Template Name</Table.Th>
              <Table.Th>Page Associated</Table.Th>
              <Table.Th></Table.Th>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {templates?.results?.map((template) => (
              <Table.Row key={template._id}>
                <Table.Td className={'md:!min-w-[24rem] md:!w-[24rem]'}>
                  {template?.name?.length > 55 ? (
                    <Tooltip text={template?.name}>
                      {template?.name?.slice(0, 55) + '...'}
                    </Tooltip>
                  ) : (
                    template?.name
                  )}
                </Table.Td>
                <Table.Td>
                  {
                    templatePageOptions.find(
                      (page) => page.value === template.cslug
                    )?.name
                  }
                </Table.Td>
                <Table.Options
                  className={'bg-white'}
                  onEdit={() => handleEdit(template)}
                  onDelete={() => handleDelete(template)}
                  onCopy={() => handleCopy(template)}
                />
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>
    </div>
  );
};

export default CreateTemplate;
