import { useEffect, useState } from 'react';

import { Button, Modal, Table } from 'antd';
import Input from '../../../components/global/components/Input';

import { useEditHrmsUserAttendanceMutation } from '../../../slices/userApiSlice';

import { TiDelete } from 'react-icons/ti';
import { toast } from 'react-toastify';

import { useHrmsContext } from '../utils/HrmsContext';

import { customConfirm } from '../../../utils/customConfirm';

const LeavesModal = ({
  openModal,
  setOpenModal,
  userId,
  leavesData,
  isRunningPayroll = false,
  startDate,
  endDate,
}) => {
  const [editUserAttendance] = useEditHrmsUserAttendanceMutation();

  const [leaves, setLeaves] = useState([]);
  const [removedLeaves, setRemovedLeaves] = useState([]);
  const [formData, setFormData] = useState({
    leavesAllowed: 0,
    leavesTaken: 0,
  });
  const user = JSON.parse(localStorage.getItem('user'))?.user;

  const { setUserDataDuringPayrollRun, setPayrollRunData } = useHrmsContext();

  const closeModal = async () => {
    const confirm = await customConfirm(
      'All unsaved progress will be lost. Are you sure you want to close this window?',
      'Delete'
    );
    if (!confirm) return;
    setOpenModal(false);
    setFormData({
      leavesAllowed: 0,
      leavesTaken: 0,
    });
    setLeaves([]);
  };

  useEffect(() => {
    if (leavesData !== undefined) {
      let temp = [];
      if (leavesData?.leaves !== undefined) {
        let leavesKey = Object.keys(leavesData?.leaves);
        for (let i of leavesKey) {
          if (leavesData?.leaves?.[i]?.isLeave) {
            temp?.push({
              date: i,
              reason: leavesData?.leaves?.[i]?.leaveReason,
            });
          }
        }
      }
      setFormData({
        leavesAllowed: leavesData?.leavesAllowed,
        leavesTaken: leavesData?.leavesTaken,
      });
      setLeaves(temp);
    }
  }, [leavesData]);

  const changeHandler = (name, value, index) => {
    setLeaves((prev) => [
      ...prev?.slice(0, index),
      {
        ...prev?.[index],
        [name]: value,
      },
      ...prev?.slice(index + 1),
    ]);
  };

  const formChangeHandler = (name, value) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const deleteLeave = async (index) => {
    const confirm = await customConfirm(
      'Are you sure you want to delete this entry?',
      'Delete'
    );
    if (!confirm) return;
    setLeaves((prev) => prev?.filter((_, elemIndex) => elemIndex !== index));
  };

  const columns = [
    {
      title: 'Date Of Leave',
      key: 'date',
      render: (_, record, index) => (
        <Input
          type="date"
          name="date"
          min={
            isRunningPayroll
              ? new Date(startDate).toISOString().split('T')[0]
              : ''
          }
          max={
            isRunningPayroll
              ? new Date(endDate).toISOString().split('T')[0]
              : ''
          }
          value={record?.date}
          onChange={(e) => changeHandler(e.target.name, e.target.value, index)}
        />
      ),
    },
    {
      title: 'Leave Reason',
      key: 'reason',
      render: (_, record, index) => (
        <Input
          name="reason"
          value={record?.reason}
          onChange={(e) => changeHandler(e.target.name, e.target.value, index)}
        />
      ),
    },
    {
      title: '',
      key: 'actions',
      render: (_, record, index) => (
        <TiDelete
          className="text-2xl text-red-500 ml-auto cursor-pointer"
          onClick={() => {
            deleteLeave(index);
            setRemovedLeaves((prev) => [...prev, record?.date]);
          }}
        />
      ),
    },
  ];

  const handleSubmit = async () => {
    const confirm = await customConfirm(
      'Submit the current information?',
      'Success'
    );
    if (!confirm) return;
    if (isRunningPayroll) {
      setPayrollRunData((prev) =>
        prev?.map((elem) => {
          if (elem?._id === userId) {
            let attendance = { ...leavesData?.allAttendance };
            for (let i of leaves) {
              attendance[i?.date] = {
                ...(attendance?.[i?.date] || {}),
                isLeave: true,
                leaveReason: i?.reason,
                leaveApprovedBy: i?.approvedBy,
              };
            }
            for (let i of removedLeaves) {
              if (attendance?.[i]?.isLeave !== undefined) {
                delete attendance?.[i]?.isLeave;
              }
              if (attendance?.[i]?.leaveReason) {
                delete attendance?.[i]?.leaveReason;
              }
              if (attendance?.[i]?.leaveApprovedBy) {
                delete attendance?.[i]?.leaveApprovedBy;
              }
            }
            return {
              ...elem,
              leavesAllowed: formData?.leavesAllowed,
              leavesTaken: formData?.leavesTaken,
              attendance,
            };
          } else {
            return elem;
          }
        })
      );
      setUserDataDuringPayrollRun((prev) => {
        let attendance = { ...leavesData?.allAttendance };
        for (let i of leaves) {
          attendance[i?.date] = {
            ...(attendance?.[i?.date] || {}),
            isLeave: true,
            leaveReason: i?.reason,
            leaveApprovedBy: i?.approvedBy,
          };
        }
        for (let i of removedLeaves) {
          if (attendance?.[i]?.isLeave !== undefined) {
            delete attendance?.[i]?.isLeave;
          }
          if (attendance?.[i]?.leaveReason) {
            delete attendance?.[i]?.leaveReason;
          }
          if (attendance?.[i]?.leaveApprovedBy) {
            delete attendance?.[i]?.leaveApprovedBy;
          }
        }
        return {
          ...(prev || {}),
          [userId]: {
            ...(prev?.[userId] || {}),
            leavesAllowed: formData?.leavesAllowed,
            leavesTaken: formData?.leavesTaken,
            attendance,
          },
        };
      });
      toast.success('Leaves Information Updated');
      closeModal();
    } else {
      let finalData = {
        ...formData,
        leavesData: leaves,
      };
      const res = await editUserAttendance({ data: finalData, id: userId });
      if (res?.error === undefined) {
        toast.success('Leaves Updated');
        closeModal();
      }
    }
  };

  return (
    <Modal
      title="Add User"
      open={openModal}
      onCancel={closeModal}
      width={1300}
      footer={[
        <Button key="back" onClick={closeModal}>
          Cancel
        </Button>,
        <Button
          key="add"
          onClick={() => {
            setLeaves((prev) => [
              ...prev,
              { date: '', reason: '', approvedBy: user?._id },
            ]);
          }}
        >
          Add
        </Button>,
        <Button
          key="submit"
          type="primary"
          // loading={loading}
          onClick={handleSubmit}
        >
          Submit
        </Button>,
      ]}
    >
      <div className="h-[45vh]">
        <div className="grid grid-cols-2 gap-2 mb-2">
          <div>
            <p className="text-slate-500 font-semibold">Leaves Allowed:</p>
            <Input
              type="number"
              name="leavesAllowed"
              onChange={(e) => formChangeHandler(e.target.name, e.target.value)}
              value={formData?.leavesAllowed}
            />
          </div>
          <div>
            <p className="text-slate-500 font-semibold">Leaves Taken:</p>
            <Input
              type="number"
              name="leavesTaken"
              onChange={(e) => formChangeHandler(e.target.name, e.target.value)}
              value={formData?.leavesTaken}
            />
          </div>
        </div>
        <div>
          <p className="text-slate-500 font-semibold">Leaves:</p>
          <Table
            columns={columns}
            //   loading={isLoadingQuery}
            className="h-[30vh] overflow-scroll"
            dataSource={leaves}
            rowKey={(record, index) => index}
            pagination={false}
            size="middle"
            scroll={{ x: true }}
            locale={{ emptyText: 'No Leaves added yet' }}
          />
        </div>
      </div>
    </Modal>
  );
};

export default LeavesModal;
