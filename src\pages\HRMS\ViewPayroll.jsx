import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Button, Input, Table, Tag } from 'antd';
import { useParams } from 'react-router-dom';
import Header from '../../components/global/components/Header';

import { useGetPayrollByIdQuery } from '../../slices/HRMS/payrollApiSlice';
import ViewSidebar from './PayrollComponents/ViewSidebar';

const ViewPayroll = () => {
  const navigate = useNavigate();
  const { id, userId } = useParams();
  const [editData, setEditData] = useState({});

  const [openSidebar, setOpenSidebar] = useState(false);
  const [payrollUsers, setPayrollUser] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  const { data: payroll } = useGetPayrollByIdQuery({ id });

  useEffect(() => {
    if (payroll?.payrollUserInfo) {
      let temp = [];
      let userIds = Object.keys(payroll?.payrollUserInfo || {});
      for (let i of userIds) {
        temp?.push(payroll?.payrollUserInfo?.[i]);
      }
      setPayrollUser(temp);
    }
  }, [payroll]);

  useEffect(() => {
    if (userId !== undefined && payroll?.payrollUserInfo !== undefined) {
      let userInfo = payroll?.payrollUserInfo?.[userId];
      setSearchTerm(userInfo?.email);
      setEditData(userInfo);
      setOpenSidebar(true);
    }
  }, [userId, payroll]);

  const columns = [
    {
      title: 'Name',
      key: 'name',
      render: (_, record) => (
        <p
          className="text-blue-500 underline cursor-pointer hover:text-blue-400"
          onClick={() => {
            setOpenSidebar(true);
            setEditData(record);
          }}
        >
          {record?.name}
        </p>
      ),
    },
    {
      title: 'Gender',
      key: 'gender',
      render: (_, record) => <p>{record?.gender}</p>,
    },
    {
      title: 'Email',
      key: 'email',
      render: (_, record) => <Tag color="blue">{record?.email}</Tag>,
    },
    {
      title: 'Contact',
      key: 'contactNumber',
      render: (_, record) => <p>{record?.contactNumber}</p>,
    },
    {
      title: 'Fixed Salary',
      key: 'fixedSalary',
      render: (_, record) => <Tag color="green">₹ {record?.fixedSalary}</Tag>,
    },
    {
      title: 'Work Hours (per Day)',
      key: 'workingHours',
      render: (_, record) => <p>{record?.workingHours}</p>,
    },
  ];

  const useSearch = (searchTerm, payrollUsers) => {
    if (searchTerm?.length === 0 || searchTerm === undefined)
      return payrollUsers;
    let res = payrollUsers?.filter((elem) =>
      elem?.email?.toLowerCase().startsWith(searchTerm.toLowerCase())
    );
    return res;
  };

  return (
    <>
      <ViewSidebar
        openSideBar={openSidebar}
        setOpenSideBar={setOpenSidebar}
        data={editData}
        startDate={payroll?.startDate}
        endDate={payroll?.endDate}
      />
      <div className="flex gap-[5px] w-full items-center justify-between">
        <Header
          title="View Payroll"
          description="Manage User Profiles for HR Purposes"
          infoTitle="Welcome to the Downtime Page"
          infoDesc="Get a comprehensive overview of machine events and performance on our Analytics Downtime Page. The Analytics Downtime Page provides a comprehensive overview of
                            machine events, offering insights into start and stop times,
                            pauses, errors, downtime, uptime, idle periods, and cycle times.  It empowers users with valuable data for improved
                            decision-making and optimizing overall operational efficiency."
          paras={['']}
        />
        <Button onClick={() => navigate('/hrms/payroll')}>Back</Button>
      </div>
      <div className="text-end">
        <Input.Search
          value={searchTerm}
          placeholder="Search using employee email"
          className="w-[30%]"
          onSearch={(value) => setSearchTerm(value)}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      <div className="mt-4">
        <Table
          columns={columns}
          loading={payroll ? false : true}
          dataSource={useSearch(searchTerm, payrollUsers)}
          rowKey={(record, index) => index}
          pagination={false}
          size="middle"
          scroll={{ x: true }}
          locale={{ emptyText: 'No Employees added yet' }}
        />
      </div>
    </>
  );
};

export default ViewPayroll;
