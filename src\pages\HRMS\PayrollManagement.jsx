import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  useDeletePayrollMutation,
  useQueryPayrollQuery,
} from '../../slices/HRMS/payrollApiSlice';
import { PAGINATION_LIMIT } from '../../utils/Constant';

import Header from '../../components/global/components/Header';
import Pagination from '../../components/global/components/Pagination';
import { customConfirm } from '../../utils/customConfirm';
import PayrollCreationModal from './PayrollComponents/PayrollCreationModal';

import { Button, Table, Tag } from 'antd';

const PayrollManagement = () => {
  const [openPayrollModal, setOpenPayrollModal] = useState(false);
  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [page, setPage] = useState(1);
  const [type, setType] = useState('desc'); //eslint-disable-line
  const [field, setField] = useState('createdAt'); //eslint-disable-line

  const navigate = useNavigate();

  const { data: payroll, isLoading } = useQueryPayrollQuery(
    { page, limit, field, type },
    { skip: !page || !limit }
  );
  const [deletePayroll] = useDeletePayrollMutation();

  const columns = [
    {
      title: 'Start Date',
      key: 'startDate',
      render: (_, record) => (
        <p>{new Date(record?.startDate)?.toDateString()}</p>
      ),
    },
    {
      title: 'End Date',
      key: 'endDate',
      render: (_, record) => <p>{new Date(record?.endDate)?.toDateString()}</p>,
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => (
        <Tag color={`${record?.status ? 'green' : 'red'}`}>
          {record?.status ? 'True' : 'False'}
        </Tag>
      ),
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <div className="flex items-center justify-center gap-2">
          <Button
            onClick={() => {
              navigate(`/hrms/payroll/run/${record?._id}`);
            }}
            className="bg-blue-500 hover:bg-blue-600 text-white"
            disabled={record?.status}
          >
            Run Payroll
          </Button>
          <Button
            onClick={() => {
              navigate(`/hrms/payroll/view/${record?._id}`);
            }}
            className="bg-green-500 hover:bg-green-600 text-white"
          >
            View
          </Button>
          <Button
            onClick={async () => {
              const confirm = await customConfirm(
                'Are you sure you want to delete this entry?',
                'Delete'
              );
              if (!confirm) return;
              await deletePayroll({ id: record?._id });
            }}
            className="bg-red-500 hover:bg-red-600 text-white"
            disabled={record?.status}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  return (
    <>
      <PayrollCreationModal
        openModal={openPayrollModal}
        setOpenModal={setOpenPayrollModal}
      />
      <div className="flex gap-[5px] w-full items-center justify-between">
        <Header
          title="Payroll Management"
          description="Manage User Profiles for HR Purposes"
          infoTitle="Welcome to the Downtime Page"
          infoDesc="Get a comprehensive overview of machine events and performance on our Analytics Downtime Page. The Analytics Downtime Page provides a comprehensive overview of
                                        machine events, offering insights into start and stop times,
                                        pauses, errors, downtime, uptime, idle periods, and cycle times.  It empowers users with valuable data for improved
                                        decision-making and optimizing overall operational efficiency."
          paras={['']}
        />
        <div>
          <Button
            onClick={() => {
              setOpenPayrollModal(true);
            }}
          >
            Create Payroll
          </Button>
        </div>
      </div>
      <div className="mt-4">
        <Table
          columns={columns}
          loading={isLoading}
          dataSource={Array.isArray(payroll?.results) ? payroll?.results : []}
          rowKey={(_, index) => index}
          pagination={false}
          size="middle"
          scroll={{ x: true }}
          locale={{ emptyText: 'No Employees added yet' }}
        />
        <Pagination
          limit={limit}
          page={page}
          totalPages={payroll?.totalPages}
          totalResults={payroll?.totalResults}
          setPage={setPage}
          setLimit={setLimit}
          className={`w-full`}
        />
      </div>
    </>
  );
};

export default PayrollManagement;
