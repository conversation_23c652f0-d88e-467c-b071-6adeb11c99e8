import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Button, Table, Tag } from 'antd';
import SalaryComponentsModal from './SalaryComponentsModal';

import { IoArrowBackCircleSharp } from 'react-icons/io5';
import { customConfirm } from '../../../utils/customConfirm';
import { useHrmsContext } from '../utils/HrmsContext';

const SalaryComponent = ({ payroll, setCurrent }) => {
  const navigate = useNavigate();
  const [openModal, setOpenModal] = useState(false);
  const [salaryComponents, setSalaryComponentInfo] = useState(false);

  const { calculatePayrollStepThreeSalary, calculatePayrollStepFourSalary } =
    useHrmsContext();

  const columns = [
    {
      title: 'Name',
      key: 'name',
      render: (_, record) => <p>{record?.name}</p>,
    },
    {
      title: 'Gender',
      key: 'gender',
      render: (_, record) => <p>{record?.gender}</p>,
    },
    {
      title: 'Email',
      key: 'email',
      render: (_, record) => <Tag color="blue">{record?.email}</Tag>,
    },
    {
      title: 'Contact',
      key: 'contactNumber',
      render: (_, record) => <p>{record?.contactNumber}</p>,
    },
    {
      title: 'Fixed Salary',
      key: 'fixedSalary',
      render: (_, record) => <Tag color="green">₹ {record?.fixedSalary}</Tag>,
    },
    {
      title: 'Salary Uptill Now',
      key: 'stepThreePay',
      render: (_, record) => (
        <Tag color="yellow">
          ₹{' '}
          {Math.ceil(
            calculatePayrollStepThreeSalary(
              record?._id,
              payroll?.startDate,
              payroll?.endDate
            )
          )}
        </Tag>
      ),
    },
    {
      title: 'Salary After Salary Component Calculations',
      key: 'stepFourPay',
      render: (_, record) => (
        <Tag color="orange">
          ₹{' '}
          {Math.ceil(
            calculatePayrollStepFourSalary(
              record?._id,
              payroll?.startDate,
              payroll?.endDate
            )
          )}
        </Tag>
      ),
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <div className="flex items-center gap-2">
          <Button
            className="bg-purple-500 hover:bg-purple-600 text-white"
            onClick={() => {
              setOpenModal(true);
              let temp = {
                salaryComponents: record?.salaryComponents,
                userId: record?._id,
              };
              setSalaryComponentInfo(temp);
            }}
          >
            Add Components
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="w-full">
      <SalaryComponentsModal
        openModal={openModal}
        setOpenModal={setOpenModal}
        startDate={payroll?.startDate}
        endDate={payroll?.endDate}
        salaryComponents={salaryComponents}
      />
      <div className="bg-white rounded-[10px] pr-2 py-2 mx-2 w-full flex justify-between">
        <div className="flex items-center gap-2">
          <IoArrowBackCircleSharp
            className="text-4xl text-blue-400 cursor-pointer"
            onClick={() => navigate('/hrms/payroll')}
          />
          <h4 className="text-slate-500">
            Manipulation of salary components (Tax)
          </h4>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={async () => {
              const confirm = await customConfirm(
                'All unsaved progress will be lost. Are you sure you want to go to the previous step?',
                'Delete'
              );
              if (!confirm) return;
              setCurrent((prev) => prev - 1);
            }}
          >
            Previous
          </Button>
          <Button
            onClick={async () => {
              const confirm = await customConfirm(
                'Go to next step?',
                'Success'
              );
              if (!confirm) return;
              setCurrent((prev) => prev + 1);
            }}
          >
            Next
          </Button>
        </div>
      </div>
      <div className="h-[60vh]">
        <Table
          columns={columns}
          loading={payroll?.users !== undefined ? false : true}
          dataSource={payroll?.users}
          rowKey={(_, index) => index}
          pagination={false}
          size="middle"
          scroll={{ x: true }}
          locale={{ emptyText: 'No Employees added yet' }}
        />
      </div>
    </div>
  );
};

export default SalaryComponent;
